RewriteEngine On

# Add CORS headers for all requests
Head<PERSON> always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, DELETE, PUT"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Don't rewrite existing files and directories
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Don't rewrite asset files by extension
RewriteRule \.(js|css|svg|png|jpg|jpeg|gif|ico|woff|woff2|ttf|eot)$ - [L]

# Don't rewrite script, image, and other asset directories
RewriteRule ^(script|image|css|js)/ - [L]

# Special rule for V2 testing
RewriteRule ^([a-f0-9]{32})/v2/?$ index-v2.php [L]

# Only rewrite video ID patterns to index.php
RewriteRule ^([a-f0-9]{32})/?$ index.php [L]