<?php
header("Content-Type: text/html; charset=utf-8");
$video_id = null;
if (isset($_SERVER['REQUEST_URI'])) {
    $request_uri = $_SERVER['REQUEST_URI'];
    if (preg_match('/\/embed\/([a-f0-9]{32})\/?/', $request_uri, $matches)) {
        $video_id = $matches[1];
    }
    else {
        $path_parts = explode('/', trim($request_uri, '/'));
        if (count($path_parts) >= 2 && $path_parts[0] === 'embed') {
            $potential_id = $path_parts[1];
            if (preg_match('/^[a-f0-9]{32}$/', $potential_id)) {
                $video_id = $potential_id;
            }
        }
    }
}
if (!$video_id) {
    echo '<div style="color:red">Invalid video ID. URL format should be: /embed/{32-character-hex-id}/</div>';
    echo '<div style="color:gray; font-size:12px;">Current URL: ' . htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'unknown') . '</div>';
    exit;
}
$wordpress_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
$secretKey = "m6mljIIxd0MIz8P6W3KK60v1dFyXzllATWtQX6yUCpu5UEDmwRAE2jrQRyfvF";
$now = time();
$exp = $now + 14400;
$r = bin2hex(random_bytes(8));
$raw = $r . ":" . $exp;
$h = hash_hmac("sha256", $raw, $secretKey);
$token = bin2hex($raw . ":" . $h);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <title>P2P Player V2</title>
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/ua-parser-js/dist/ua-parser.min.js" as="script" />
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/p2p-media-loader-core@latest/build/p2p-media-loader-core.min.js" as="script" />
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/p2p-media-loader-hlsjs@latest/build/p2p-media-loader-hlsjs.min.js" as="script" />
    <link rel="preload" href="https://ssl.p.jwpcdn.com/player/v/8.18.2/jwplayer.js" as="script" />
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/@hola.org/jwplayer-hlsjs@latest/dist/jwplayer.hlsjs.min.js" as="script" />
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/hls.js@0.14.17" as="script" />
    <script src="https://cdn.jsdelivr.net/npm/ua-parser-js/dist/ua-parser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/p2p-media-loader-core@latest/build/p2p-media-loader-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/p2p-media-loader-hlsjs@latest/build/p2p-media-loader-hlsjs.min.js"></script>
    <script src="https://ssl.p.jwpcdn.com/player/v/8.18.2/jwplayer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@hola.org/jwplayer-hlsjs@latest/dist/jwplayer.hlsjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@0.14.17"></script>
    <script src="https://cdn.jsdelivr.net/npm/devtools-detector"></script>
    <script src="/embed/script/js/init-devtool.js"></script>
    <style>
        * {margin: 0;padding: 0;box-sizing: border-box;}
        body {height: 100vh;background-color: #000;overflow: hidden;}
        #mediaplayer {width: 100%;height: 100%;}
        #loading {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);color: white;font-family: Arial, sans-serif;text-align: center;z-index: 1000;}
        #loading img {width: 50px;height: 50px;animation: spin 1s linear infinite;}
        @keyframes spin {0% { transform: rotate(0deg); }100% { transform: rotate(360deg); }}
        .error {color: red;font-size: 18px;margin-top: 20px;}
    </style>
</head>
<body>
    <div id="loading">
        <img src="/embed/image/loading.svg" alt="Loading..." />
    </div>
    <div id="mediaplayer"></div>
    <div id="error-log" class="error-message"></div>
	<script id="token-script">
		var v="<?php echo htmlspecialchars($video_id,ENT_QUOTES,'UTF-8');?>";
		var token="<?php echo htmlspecialchars($token,ENT_QUOTES,'UTF-8');?>";
		var wordpressUrl="<?php echo htmlspecialchars($wordpress_url,ENT_QUOTES,'UTF-8');?>";
    </script>
    <script src="/embed/script/js/jwplayers.js"></script>
</body>
</html>
