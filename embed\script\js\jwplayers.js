document.addEventListener('DOMContentLoaded', function() {
    if (typeof v !== 'undefined' && typeof token !== 'undefined' && v && token) {
        getVideoUrl();
    } else {
        showError('Missing video ID or token');
    }
});

function showError(message) {
    var errorElement = document.getElementById('error-log');
    if (errorElement) {
        errorElement.textContent = message;
    }
    var loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

function getVideoUrl() {
    if (typeof wordpressUrl === 'undefined' || !wordpressUrl) {
        showError('WordPress URL not configured');
        return;
    }

    console.log('Video ID:', v);
    console.log('Token:', token.substring(0, 20) + '...');
    console.log('WordPress URL:', wordpressUrl);

    testAjax(function() {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', wordpressUrl + '/wp-admin/admin-ajax.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                console.log('Response status:', xhr.status);
                console.log('Response text:', xhr.responseText);

                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.success && response.data && response.data.url) {
                            initializePlayer(response.data.url);
                        } else {
                            showError('Video not found: ' + (response.data ? response.data.message : 'Unknown error'));
                        }
                    } catch (e) {
                        showError('Invalid response from server: ' + e.message);
                    }
                } else {
                    showError('Server error: ' + xhr.status + ' - ' + xhr.responseText);
                }
            }
        };

        var formData = 'action=get_video_url&video_id=' + encodeURIComponent(v) + '&nonce=' + encodeURIComponent(token);
        xhr.send(formData);
    });
}

function testAjax(callback) {
    var xhr = new XMLHttpRequest();
    xhr.open('POST', wordpressUrl + '/wp-admin/admin-ajax.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                console.log('AJAX test successful');
                callback();
            } else {
                showError('AJAX test failed: ' + xhr.status);
            }
        }
    };

    xhr.send('action=test_video_url');
}

function initializePlayer(videoUrl) {
    var loadingElement = document.getElementById('loading');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }

    if (typeof jwplayer === 'undefined') {
        showError('JW Player not loaded');
        return;
    }

    console.log('Initializing player with URL:', videoUrl);

    testVideoUrl(videoUrl, function(isAccessible) {
        if (!isAccessible) {
            console.warn('Video URL may not be accessible, but trying anyway...');
        }
        setupPlayer(videoUrl);
    });
}

function testVideoUrl(url, callback) {
    var xhr = new XMLHttpRequest();
    xhr.open('HEAD', url, true);
    xhr.timeout = 5000;

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            callback(xhr.status === 200);
        }
    };

    xhr.onerror = function() {
        callback(false);
    };

    xhr.ontimeout = function() {
        callback(false);
    };

    try {
        xhr.send();
    } catch (e) {
        callback(false);
    }
}

function setupPlayer(videoUrl) {
    var player = jwplayer("mediaplayer");
    
    var hlsConfig = {
        liveSyncDurationCount: 3,
        liveMaxLatencyDurationCount: 5,
        maxBufferLength: 30,
        maxMaxBufferLength: 600,
        enableWorker: true,
        lowLatencyMode: false,
        backBufferLength: -1,
        fragLoadingTimeOut: 4000,
        fragLoadingMaxRetry: 10,
        fragLoadingRetryDelay: 0,
        manifestLoadingMaxRetry: 10,
        manifestLoadingRetryDelay: 0,
        levelLoadingMaxRetry: 10,
        maxBufferLength: 200,
        maxMaxBufferLength: 600
    };
    
    var playerConfig = {
        image: "",
        width: "100%",
        height: "100%",
        controls: true,
        mute: false,
        preload: "auto",
        primary: "html5",
        autostart: true,
        stretching: "uniform",
        displaytitle: false,
        hlsjsConfig: hlsConfig,
        sources: [
            {
                file: videoUrl,
                type: "application/x-mpegURL"
            }
        ],
        maxBufferLength: 200,
        maxMaxBufferLength: 600,
        crossdomain: true,
        networkTimeout: 30000,
        fallback: true
    };
    
    player.setup(playerConfig);

    player.on('error', function(e) {
        console.error('JW Player Error:', e);
        showError('Player Error: ' + e.message + ' (Code: ' + e.code + ')');
    });

    player.on('ready', function() {
        console.log('Player ready');
    });

    player.on('play', function() {
        console.log('Player started playing');
    });

    var hls;
    var fragmentCache = {};
    
    if (typeof Hls !== 'undefined' && Hls.isSupported() && 
        typeof p2pml !== 'undefined' && p2pml.hlsjs && p2pml.hlsjs.Engine && 
        p2pml.hlsjs.Engine.isSupported()) {
        
        var p2pConfig = {
            simultaneousP2PDownloads: 40,
            simultaneousHttpDownloads: 20,
            p2pDownloadMaxPriority: 1,
            p2pDownloadMaxPrioritySkipIfNoPeers: 1,
            httpDownloadMaxPriority: 80,
            httpDownloadProbability: 6,
            httpDownloadMaxPriority: 4,
            httpUseRanges: 1,
            trackerAnnounce: ["wss://tracker.webtorrent.dev"],
            rtcConfig: {}
        };
        
        p2pConfig.rtcConfig.iceServers = [
            { urls: "stun:stun.l.google.com:19302" },
            { urls: "stun:stun2.l.google.com:19302" },
            { urls: "stun:stun3.l.google.com:19302" }
        ];
        
        var p2pEngine = new p2pml.hlsjs.Engine(p2pConfig);
        
        if (typeof jwplayer_hls_provider !== 'undefined') {
            jwplayer_hls_provider.attach();
        }
        
        hls = p2pml.hlsjs.createJwPlayerProvider(player, {
            loader: p2pEngine.createLoaderClass(),
            fragLoadingRetryDelay: 0,
            fragLoadingTimeOut: 4000,
            fragLoadingMaxRetry: 10
        });
        
        if (hls && typeof hls.on === 'function') {
            hls.on(Hls.Events.FRAG_LOADING, function(event, data) {
                fragmentCache[data.frag.sn] = {
                    startTime: Date.now(),
                    loaded: false
                };
            });
            
            hls.on(Hls.Events.FRAG_LOADED, function(event, data) {
                if (fragmentCache[data.frag.sn]) {
                    fragmentCache[data.frag.sn].loaded = true;
                }
            });
            
            hls.on(Hls.Events.FRAG_PARSED, function(event, data) {
                if (fragmentCache[data.frag.sn]) {
                    delete fragmentCache[data.frag.sn];
                }
            });
            
            hls.on(Hls.Events.ERROR, function(event, data) {
                if (data.type === Hls.ErrorTypes.NETWORK_ERROR) {
                    if (data.details && data.details.indexOf('ERR_QUIC_PROTOCOL_ERROR') !== -1) {
                        hls.stopLoad();
                        hls.startLoad(-1);
                        return;
                    }
                    
                    if (data.details === Hls.ErrorDetails.FRAG_LOAD_TIMEOUT || 
                        data.details === Hls.ErrorDetails.FRAG_LOAD_ERROR) {
                        hls.stopLoad();
                        hls.startLoad(-1);
                        return;
                    }
                    
                    hls.startLoad(-1);
                } else if (data.type === Hls.ErrorTypes.MEDIA_ERROR) {
                    hls.recoverMediaError();
                }
            });
            
            setInterval(function() {
                var currentTime = Date.now();
                for (var fragSn in fragmentCache) {
                    if (fragmentCache.hasOwnProperty(fragSn)) {
                        var fragment = fragmentCache[fragSn];
                        if (!fragment.loaded && currentTime - fragment.startTime >= 5000) {
                            hls.stopLoad();
                            hls.startLoad(-1);
                            break;
                        }
                    }
                }
            }, 1000);
        }
    }
    
    player.on('ready', function() {
        var loadingDiv = document.getElementById('loading');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
        
        var playerDiv = document.getElementById('mediaplayer');
        if (playerDiv) {
            playerDiv.style.display = 'block';
        }
        
        setupBufferMonitoring(player, hls);
    });
    
    player.on('error', function(event) {
        showError('Player error: ' + (event.message || 'Unknown error'));
    });
}

function setupBufferMonitoring(player, hls) {
    setInterval(function() {
        var container = player.getContainer().querySelector('video');
        if (!container) return;
        
        var currentTime = container.currentTime;
        var duration = container.duration;
        var bufferedLength = 0;
        
        for (var i = 0; i < container.buffered.length; i++) {
            var start = container.buffered.start(i);
            var end = container.buffered.end(i);
            if (currentTime >= start && currentTime <= end) {
                bufferedLength = end - currentTime;
                break;
            }
        }
        
        if (currentTime > 0 && currentTime < (duration - 10)) {
            if (bufferedLength < 3) {
                if (hls && typeof hls.startLoad === 'function') {
                    hls.startLoad(-1);
                }
            }
        }
    }, 500);
} 