<?php
if (!defined('ABSPATH')) exit;

function custom_movieyear() {
    $labels = array(
        'name' => 'Movie Year',
        'singular_name' => 'Movie Year',
        'menu_name' => 'Movie Year',
        'all_items' => 'All Movie Year',
        'parent_item' => 'Parent Movie Year',
        'parent_item_colon' => 'Parent Movie Year:',
        'new_item_name' => 'New Movie Year',
        'add_new_item' => 'Add Movie Year',
        'edit_item' => 'Edit Movie Year',
        'update_item' => 'Update Movie Year',
        'view_item' => 'View Movie Year',
        'separate_items_with_commas' => 'Separate Movie Year with commas',
        'add_or_remove_items' => 'Add or remove Movie Year',
        'choose_from_most_used' => 'Choose from the most used',
        'popular_items' => 'Popular Movie Year',
        'search_items' => 'Search Movie Year',
        'not_found' => 'Not Found',
        'no_terms' => 'No Movie Year',
        'items_list' => 'Movie Year list',
        'items_list_navigation' => 'Movie Year list navigation'
    );
    $args = array(
        'labels' => $labels,
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true
    );
    register_taxonomy('years', array('movie', 'serie', 'anime'), $args);
}

function custom_imdb() {
    $labels = array(
        'name' => 'IMDB Rating',
        'singular_name' => 'IMDB Rating',
        'menu_name' => 'IMDB Rating',
        'all_items' => 'All IMDB Rating',
        'parent_item' => 'Parent IMDB Rating',
        'parent_item_colon' => 'Parent IMDB Rating:',
        'new_item_name' => 'New IMDB Rating',
        'add_new_item' => 'Add IMDB Rating',
        'edit_item' => 'Edit IMDB Rating',
        'update_item' => 'Update IMDB Rating',
        'view_item' => 'View IMDB Rating',
        'separate_items_with_commas' => 'Separate IMDB Rating with commas',
        'add_or_remove_items' => 'Add or remove IMDB Rating',
        'choose_from_most_used' => 'Choose from the most used',
        'popular_items' => 'Popular IMDB Rating',
        'search_items' => 'Search IMDB Rating',
        'not_found' => 'Not Found',
        'no_terms' => 'No IMDB Rating',
        'items_list' => 'IMDB Rating list',
        'items_list_navigation' => 'IMDB Rating list navigation'
    );
    $args = array(
        'labels' => $labels,
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true
    );
    register_taxonomy('imdb', array('movie', 'serie', 'anime'), $args);
}

function custom_post_type_movie() {
    $labels = array(
        'name' => 'Movie',
        'singular_name' => 'Movie',
        'menu_name' => 'ภาพยนตร์',
        'name_admin_bar' => 'Movie',
        'add_new' => 'Add New',
        'add_new_item' => 'Add New Movie',
        'new_item' => 'New Movie',
        'edit_item' => 'Edit Movie',
        'view_item' => 'View Movie',
        'all_items' => 'All Movies',
        'search_items' => 'Search Movies',
        'parent_item_colon' => 'Parent Movies:',
        'not_found' => 'No movies found.',
        'not_found_in_trash' => 'No movies found in Trash.'
    );
    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'movie'),
        'capability_type' => 'post',
        'has_archive' => true,
        'hierarchical' => false,
        'menu_position' => null,
        'supports' => array('title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments'),
        'taxonomies' => array('category', 'post_tag')
    );
    register_post_type('movie', $args);
}

function custom_post_type_serie() {
    $labels = array(
        'name' => 'Serie',
        'singular_name' => 'Serie',
        'menu_name' => 'ซีรี่ย์',
        'name_admin_bar' => 'Serie',
        'add_new' => 'Add New',
        'add_new_item' => 'Add New Serie',
        'new_item' => 'New Serie',
        'edit_item' => 'Edit Serie',
        'view_item' => 'View Serie',
        'all_items' => 'All Series',
        'search_items' => 'Search Series',
        'parent_item_colon' => 'Parent Series:',
        'not_found' => 'No series found.',
        'not_found_in_trash' => 'No series found in Trash.'
    );
    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'serie'),
        'capability_type' => 'post',
        'has_archive' => true,
        'hierarchical' => false,
        'menu_position' => null,
        'supports' => array('title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments'),
        'taxonomies' => array('category', 'post_tag')
    );
    register_post_type('serie', $args);
}

function custom_post_type_anime() {
    $labels = array(
        'name' => 'Anime',
        'singular_name' => 'Anime',
        'menu_name' => 'อนิเมะ',
        'name_admin_bar' => 'Anime',
        'add_new' => 'Add New',
        'add_new_item' => 'Add New Anime',
        'new_item' => 'New Anime',
        'edit_item' => 'Edit Anime',
        'view_item' => 'View Anime',
        'all_items' => 'All Anime',
        'search_items' => 'Search Anime',
        'parent_item_colon' => 'Parent Anime:',
        'not_found' => 'No anime found.',
        'not_found_in_trash' => 'No anime found in Trash.'
    );
    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'anime'),
        'capability_type' => 'post',
        'has_archive' => true,
        'hierarchical' => false,
        'menu_position' => null,
        'supports' => array('title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments'),
        'taxonomies' => array('category', 'post_tag')
    );
    register_post_type('anime', $args);
}

function custom_post_type_adult() {
    $labels = array(
        'name' => 'Adult',
        'singular_name' => 'Adult',
        'menu_name' => 'หนังโป๊',
        'name_admin_bar' => 'Adult',
        'add_new' => 'Add New',
        'add_new_item' => 'Add New Adult',
        'new_item' => 'New Adult',
        'edit_item' => 'Edit Adult',
        'view_item' => 'View Adult',
        'all_items' => 'All Adult',
        'search_items' => 'Search Adult',
        'parent_item_colon' => 'Parent Adult:',
        'not_found' => 'No Adult found.',
        'not_found_in_trash' => 'No Adult found in Trash.'
    );
    $args = array(
        'labels' => $labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'adult'),
        'capability_type' => 'post',
        'has_archive' => true,
        'hierarchical' => false,
        'menu_position' => null,
        'supports' => array('title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments'),
        'taxonomies' => array('post_tag', 'adult_category')
    );
    register_post_type('adult', $args);
}

function serie_custom_taxonomy() {
    $labels = array(
        'name' => 'Serie Categories',
        'singular_name' => 'Serie Category',
        'search_items' => 'Search Serie Categories',
        'all_items' => 'All Serie Categories',
        'parent_item' => 'Parent Serie Category',
        'parent_item_colon' => 'Parent Serie Category:',
        'edit_item' => 'Edit Serie Category',
        'update_item' => 'Update Serie Category',
        'add_new_item' => 'Add New Serie Category',
        'new_item_name' => 'New Serie Category Name',
        'menu_name' => 'Serie Categories'
    );
    $args = array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'series/category')
    );
    register_taxonomy('serie_category', array('serie'), $args);
}

function anime_custom_taxonomy() {
    $labels = array(
        'name' => 'Anime Categories',
        'singular_name' => 'Anime Category',
        'search_items' => 'Search Anime Categories',
        'all_items' => 'All Anime Categories',
        'parent_item' => 'Parent Anime Category',
        'parent_item_colon' => 'Parent Anime Category:',
        'edit_item' => 'Edit Anime Category',
        'update_item' => 'Update Anime Category',
        'add_new_item' => 'Add New Anime Category',
        'new_item_name' => 'New Anime Category Name',
        'menu_name' => 'Anime Categories'
    );
    $args = array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'animes/category')
    );
    register_taxonomy('anime_category', array('anime'), $args);
}

function adult_custom_taxonomy() {
    $labels = array(
        'name' => 'Adult Categories',
        'singular_name' => 'Adult Category',
        'search_items' => 'Search Adult Categories',
        'all_items' => 'All Adult Categories',
        'parent_item' => 'Parent Adult Category',
        'parent_item_colon' => 'Parent Adult Category:',
        'edit_item' => 'Edit Adult Category',
        'update_item' => 'Update Adult Category',
        'add_new_item' => 'Add New Adult Category',
        'new_item_name' => 'New Adult Category Name',
        'menu_name' => 'Adult Categories'
    );
    $args = array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'adults/category')
    );
    register_taxonomy('adult_category', array('adult'), $args);
}

function display_years_link() {
    global $post;
    $years_terms = wp_get_post_terms($post->ID, 'years');
    if (!empty($years_terms) && !is_wp_error($years_terms)) {
        foreach ($years_terms as $term) {
            echo '<a href="' . esc_url(get_term_link($term)) . '" rel="tag" style="font-size:18px;color:#fff;margin:4px;width:120px;">ปีที่ฉาย ' . esc_html($term->name) . '</a>';
        }
    }
}

function check_imdb_rating($post_id) {
    if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
        return;
    }
    if (!metadata_exists('post', $post_id, 'imdb')) {
        $imdb_terms = get_the_terms($post_id, 'imdb');
        if ($imdb_terms && !is_wp_error($imdb_terms) && !empty($imdb_terms)) {
            $imdb_rating = $imdb_terms[0]->name;
            update_post_meta($post_id, 'imdb', $imdb_rating);
        } else {
            $imdb_rating = round(mt_rand(31, 55) / 10, 1);
            update_post_meta($post_id, 'imdb', $imdb_rating);
        }
    }
}
add_action('save_post', 'check_imdb_rating');

function display_imdb_rating() {
    global $post;
    $imdb_rating = get_the_terms($post->ID, 'imdb');
    if ($imdb_rating && !is_wp_error($imdb_rating) && !empty($imdb_rating)) {
        foreach ($imdb_rating as $term) {
            echo $term->name;
        }
    } else {
        echo round(mt_rand(31, 55) / 10, 1);
    }
}

function create_years_and_imdb_terms_on_plugin_activation() {
    $y = (int) date('Y');
    if ($y % 2 !== 0) {
        $y++;
    }
    $start = $y - 87;
    if (taxonomy_exists('years')) {
        for ($i = $start; $i <= $y; $i++) {
            if (!term_exists($i, 'years')) {
                wp_insert_term($i, 'years');
            }
        }
    }
    if (taxonomy_exists('imdb')) {
        for ($i = 10; $i <= 100; $i++) {
            $score = number_format($i / 10, 1);
            if (!term_exists($score, 'imdb')) {
                wp_insert_term($score, 'imdb');
            }
        }
    }
    create_old_and_new_movie_menus_with_years();
}

function create_old_and_new_movie_menus_with_years() {
    $all = get_terms([
        'taxonomy' => 'years',
        'hide_empty' => false
    ]);
    if (empty($all) || is_wp_error($all)) {
        return;
    }
    usort($all, function($a, $b) {
        return (int)$b->name - (int)$a->name;
    });
    $cnt = count($all);
    if ($cnt < 88) {
        return;
    }
    $new_part = array_slice($all, 0, 44);
    $old_part = array_slice($all, 44, 44);
    $old_obj = wp_get_nav_menu_object('Old Movies');
    if ($old_obj) {
        wp_delete_term($old_obj->term_id, 'nav_menu');
    }
    $old_id = wp_create_nav_menu('Old Movies');
    if (!is_wp_error($old_id)) {
        foreach ($old_part as $term) {
            $url = home_url('/year/' . $term->slug);
            wp_update_nav_menu_item($old_id, 0, [
                'menu-item-title' => $term->name,
                'menu-item-url' => $url,
                'menu-item-status' => 'publish'
            ]);
        }
    }
    $new_obj = wp_get_nav_menu_object('New Movies');
    if ($new_obj) {
        wp_delete_term($new_obj->term_id, 'nav_menu');
    }
    $new_id = wp_create_nav_menu('New Movies');
    if (!is_wp_error($new_id)) {
        foreach ($new_part as $term) {
            $url = home_url('/year/' . $term->slug);
            wp_update_nav_menu_item($new_id, 0, [
                'menu-item-title' => $term->name,
                'menu-item-url' => $url,
                'menu-item-status' => 'publish'
            ]);
        }
    }
}

function get_post_by_title($title, $allowed) {
    global $wpdb;
    $title = sanitize_text_field($title);
    if (empty($allowed)) {
        return null;
    }
    $allowed_list = array();
    foreach ($allowed as $pt) {
        $allowed_list[] = esc_sql($pt);
    }
    $allowed_in = "'" . implode("','", $allowed_list) . "'";
    $query = "SELECT ID FROM $wpdb->posts WHERE post_title LIKE %s AND post_status = 'publish' AND post_type IN ($allowed_in) LIMIT 1";
    $post_id = $wpdb->get_var($wpdb->prepare($query, '%' . $wpdb->esc_like($title) . '%'));
    return $post_id ? $post_id : null;
}

class WP_Post_Remote_Receiver_Pro {
    private static $inst;
    public static function instance() {
        if (!isset(self::$inst)) {
            self::$inst = new self();
        }
        return self::$inst;
    }
    public function __construct() {
        add_action('init', array($this, 'register_dynamic_cpts'), 11);
        add_action('admin_init', array($this, 'hide_defaults'));
    }
    public function register_dynamic_cpts() {
        $siteSettings = get_option('mytheme_site_settings', array());
        $allowed = isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types']) ? $siteSettings['allowed_post_types'] : array();
        if (in_array('movie', $allowed)) {
            custom_post_type_movie();
            custom_movieyear();
            custom_imdb();
        }
        if (in_array('serie', $allowed)) {
            custom_post_type_serie();
            custom_movieyear();
            custom_imdb();
            serie_custom_taxonomy();
        }
        if (in_array('anime', $allowed)) {
            custom_post_type_anime();
            anime_custom_taxonomy();
            custom_movieyear();
            custom_imdb();
        }
        if (in_array('adult', $allowed)) {
            custom_post_type_adult();
            adult_custom_taxonomy();
        }
    }
    public function hide_defaults() {
        remove_menu_page('edit.php');
        remove_menu_page('edit.php?post_type=page');
    }
    public function process_incoming($d) {
        wprrp_log('process_incoming data=' . print_r($d, true));
        $pt = $d['post_type'] ?? '';
        $siteSettings = get_option('mytheme_site_settings', array());
        $allowed = isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types']) ? $siteSettings['allowed_post_types'] : array();
        wprrp_log('Allowed post types: ' . print_r($allowed, true));
        if (!in_array($pt, $allowed)) { 
            wprrp_log('process_incoming: post_type ' . $pt . ' not allowed'); 
            return; 
        }
        if ($pt === 'adult') {
            if (isset($d['adult_categories']) && is_array($d['adult_categories'])) {
                foreach ($d['adult_categories'] as $cat) {
                    if ($cat === 'Picpost AI') {
                        wprrp_log('process_incoming: adult post blocked due to Picpost AI category');
                        return;
                    }
                }
            }
        }
        $ap = isset($siteSettings['ai']) ? $siteSettings['ai'] : 'none';
        $oid = intval($d['original_post_id'] ?? 0);
        $ti = sanitize_text_field($d['title'] ?? '');
		if ($ti === 'บันทึกฉบับร่างอัตโนมัติ') { wprrp_log('process_incoming: rejected post with title บันทึกฉบับร่างอัตโนมัติ'); return; }
        $co = wp_kses_post($d['content'] ?? '');
        wprrp_log('will create/update post => original_id=' . $oid . ' title=' . $ti);
        $lid = $this->find_local($pt, $oid, $ti, $allowed);
        if (!$lid) {
            $rand_seconds = rand(0, 54000);
            $backdate = time() - $rand_seconds;
            $post_date = date('Y-m-d H:i:s', $backdate);
            $post_date_gmt = gmdate('Y-m-d H:i:s', $backdate);
            $new_post = array(
                'post_title'    => $ti,
                'post_content'  => $co,
                'post_status'   => 'publish',
                'post_type'     => $pt,
                'post_name'     => '',
                'post_date'     => $post_date,
                'post_date_gmt' => $post_date_gmt,
            );
            $lid = wp_insert_post($new_post);
            if (is_wp_error($lid)) { 
                wprrp_log('wp_insert_post error=' . print_r($lid->get_error_messages(), true)); 
            } else { 
                wprrp_log('wp_insert_post success => post_id=' . $lid); 
            }
            update_post_slug($lid);
        } else {
            list($english_title, $thai_title, $year) = split_title($lid);
            $prefix = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 5);
            $suffix = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 5);
            $modified_slug = $prefix . '-' . sanitize_title($english_title) . '-' . sanitize_title($year) . '-' . $suffix;
            $upd = array(
                'ID'           => $lid,
                'post_title'   => $ti,
                'post_content' => $co,
                'post_name'    => $modified_slug,
                'post_type'    => $pt
            );
            $res = wp_update_post($upd);
            if (is_wp_error($res)) { 
                wprrp_log('wp_update_post error=' . print_r($res->get_error_messages(), true)); 
            } else { 
                wprrp_log('wp_update_post success => post_id=' . $res); 
            }
        }
        if (!$lid || is_wp_error($lid)) { 
            wprrp_log('cannot insert/update => post=' . print_r($lid, true)); 
            return; 
        }
        update_post_meta($lid, 'original_post_id', $oid);
        $this->assign_tax($lid, $d, $pt);
        $img = $d['image'] ?? '';
        if ($img) { 
            wprrp_log('maybe_featured => image url=' . $img); 
            download_and_set_featured_image($lid, $img); 
        }
        if ($pt === 'movie') {
            delete_post_meta($lid, 'gdrivethai');
            delete_post_meta($lid, 'gdrivesub');
            delete_post_meta($lid, 'trailer');
            delete_post_meta($lid, 'duration');
            if (isset($d['imdb'])) update_post_meta($lid, 'imdb', sanitize_text_field($d['imdb']));
            if (isset($d['duration'])) update_post_meta($lid, 'duration', sanitize_text_field($d['duration']));
                    if (isset($d['gdrivethai'])) {
            $url = sanitize_text_field($d['gdrivethai']);
            if (!empty($url)) {
                $meta_data = array(
                    'url' => $url,
                    'md5_id' => md5($url)
                );
                update_post_meta($lid, 'gdrivethai', $meta_data);
            }
        }
        if (isset($d['gdrivesub'])) {
            $url = sanitize_text_field($d['gdrivesub']);
            if (!empty($url)) {
                $meta_data = array(
                    'url' => $url,
                    'md5_id' => md5($url)
                );
                update_post_meta($lid, 'gdrivesub', $meta_data);
            }
        }
            if (isset($d['trailer'])) update_post_meta($lid, 'trailer', sanitize_text_field($d['trailer']));
        } elseif ($pt === 'serie' || $pt === 'anime') {
            delete_post_meta($lid, 'serie_dubbed');
            delete_post_meta($lid, 'serie_subbed');
            delete_post_meta($lid, 'trailer');
            delete_post_meta($lid, 'duration');
            if (isset($d['imdb'])) update_post_meta($lid, 'imdb', sanitize_text_field($d['imdb']));
            if (isset($d['duration'])) update_post_meta($lid, 'duration', sanitize_text_field($d['duration']));
            if (isset($d['serie_dubbed'])) {
                if (is_array($d['serie_dubbed'])) {
                    if (empty($d['serie_dubbed'])) { 
                        update_post_meta($lid, 'serie_dubbed', '');
                        wprrp_log('serie_dubbed saved as empty string');
                    } else {
                        $urls_with_md5 = array();
                        foreach ($d['serie_dubbed'] as $url) {
                            if (!empty($url)) {
                                $urls_with_md5[] = array(
                                    'url' => $url,
                                    'md5_id' => md5($url)
                                );
                            } else {
                                $urls_with_md5[] = array(
                                    'url' => '',
                                    'md5_id' => ''
                                );
                            }
                        }
                        update_post_meta($lid, 'serie_dubbed', $urls_with_md5);
                        $saved_data = get_post_meta($lid, 'serie_dubbed', true);
                        wprrp_log('serie_dubbed saved array format: ' . print_r($saved_data, true));
                    }
                } else {
                    $url = sanitize_text_field($d['serie_dubbed']);
                    if (!empty($url)) {
                        if (strpos($url, '[') === 0 || strpos($url, '["') === 0) {
                            $decoded_urls = json_decode($url, true);
                            if (is_array($decoded_urls)) {
                                $urls_with_md5 = array();
                                foreach ($decoded_urls as $single_url) {
                                    if (!empty($single_url)) {
                                        $urls_with_md5[] = array(
                                            'url' => $single_url,
                                            'md5_id' => md5($single_url)
                                        );
                                    } else {
                                        $urls_with_md5[] = array(
                                            'url' => '',
                                            'md5_id' => ''
                                        );
                                    }
                                }
                                update_post_meta($lid, 'serie_dubbed', $urls_with_md5);
                                $saved_data = get_post_meta($lid, 'serie_dubbed', true);
                                wprrp_log('serie_dubbed saved array format (JSON decoded): ' . print_r($saved_data, true));
                            } else {
                                $meta_data = array(
                                    'url' => $url,
                                    'md5_id' => md5($url)
                                );
                                update_post_meta($lid, 'serie_dubbed', array($meta_data));
                                $saved_data = get_post_meta($lid, 'serie_dubbed', true);
                                wprrp_log('serie_dubbed saved array format (single URL): ' . print_r($saved_data, true));
                            }
                        } else {
                            $meta_data = array(
                                'url' => $url,
                                'md5_id' => md5($url)
                            );
                            update_post_meta($lid, 'serie_dubbed', array($meta_data));
                            $saved_data = get_post_meta($lid, 'serie_dubbed', true);
                            wprrp_log('serie_dubbed saved array format (single URL direct): ' . print_r($saved_data, true));
                        }
                    }
                }
            }
            if (isset($d['serie_subbed'])) {
                if (is_array($d['serie_subbed'])) {
                    if (empty($d['serie_subbed'])) { 
                        update_post_meta($lid, 'serie_subbed', ''); 
                    } else {
                        $urls_with_md5 = array();
                        foreach ($d['serie_subbed'] as $url) {
                            if (!empty($url)) {
                                $urls_with_md5[] = array(
                                    'url' => $url,
                                    'md5_id' => md5($url)
                                );
                            } else {
                                $urls_with_md5[] = array(
                                    'url' => '',
                                    'md5_id' => ''
                                );
                            }
                        }
                        update_post_meta($lid, 'serie_subbed', $urls_with_md5);
                    }
                } else {
                    $url = sanitize_text_field($d['serie_subbed']);
                    if (!empty($url)) {
                        if (strpos($url, '[') === 0 || strpos($url, '["') === 0) {
                            $decoded_urls = json_decode($url, true);
                            if (is_array($decoded_urls)) {
                                $urls_with_md5 = array();
                                foreach ($decoded_urls as $single_url) {
                                    if (!empty($single_url)) {
                                        $urls_with_md5[] = array(
                                            'url' => $single_url,
                                            'md5_id' => md5($single_url)
                                        );
                                    } else {
                                        $urls_with_md5[] = array(
                                            'url' => '',
                                            'md5_id' => ''
                                        );
                                    }
                                }
                                update_post_meta($lid, 'serie_subbed', $urls_with_md5);
                            } else {
                                $meta_data = array(
                                    'url' => $url,
                                    'md5_id' => md5($url)
                                );
                                update_post_meta($lid, 'serie_subbed', array($meta_data));
                            }
                        } else {
                            $meta_data = array(
                                'url' => $url,
                                'md5_id' => md5($url)
                            );
                            update_post_meta($lid, 'serie_subbed', array($meta_data));
                        }
                    }
                }
            }
            if (isset($d['trailer'])) update_post_meta($lid, 'trailer', sanitize_text_field($d['trailer']));
        } elseif ($pt === 'adult') {
            delete_post_meta($lid, 'adult_drive_link');
            delete_post_meta($lid, 'adult_image_download_url');
            delete_post_meta($lid, 'duration');
            if (isset($d['duration'])) update_post_meta($lid, 'duration', sanitize_text_field($d['duration']));
            if (isset($d['adult_drive_link'])) {
                $url = esc_url_raw($d['adult_drive_link']);
                if (!empty($url)) {
                    $meta_data = array(
                        'url' => $url,
                        'md5_id' => md5($url)
                    );
                    update_post_meta($lid, 'adult_drive_link', $meta_data);
                }
            }
            if (isset($d['adult_image_download_url'])) update_post_meta($lid, 'adult_image_download_url', esc_url_raw($d['adult_image_download_url']));
        }
        if ($ap !== 'none') {
            $keys = $d['ai_keys'] ?? array();
            wprrp_log('apply_ai => provider=' . $ap . ' keys=' . print_r($keys, true));
            $this->apply_ai($lid, $ti, $co, $ap, $keys);
        }
        $cb = $d['data_callback_url'] ?? '';
        $sid = intval($d['data_site_id'] ?? 0);
        if ($cb && $sid) {
            $tok = get_option('wprrp_receiver_token', '(None yet)');
            $callback_data = array(
                'site_id' => $sid,
                'post_id' => $oid,
                'status'  => 'success',
                'message' => 'Receiver done set post_type'
            );
            $res = wp_remote_post($cb, array(
                'body'    => json_encode($callback_data),
                'headers' => array(
                    'Content-Type'  => 'application/json',
                    'Authorization' => 'Bearer ' . $tok
                ),
                'timeout' => 20
            ));
            if (is_wp_error($res)) { 
                wprrp_log('callback => ERROR => ' . $res->get_error_message()); 
            } else { 
                $code = wp_remote_retrieve_response_code($res); 
                $body = wp_remote_retrieve_body($res); 
                wprrp_log('callback => code=' . $code . ' body=' . $body); 
            }
        }
    }
    public function find_local($pt, $oid, $title, $allowed) {
        wprrp_log('find_local: Allowed post types: ' . print_r($allowed, true));
        if (!in_array($pt, $allowed)) { 
            wprrp_log('find_local => post_type ' . $pt . ' not allowed'); 
            return 0; 
        }
        if ($oid) {
            $fx = get_posts(array(
                'post_type'      => $pt,
                'meta_key'       => 'original_post_id',
                'meta_value'     => $oid,
                'posts_per_page' => 1,
                'post_status'    => 'any'
            ));
            if ($fx && count($fx) > 0) { 
                wprrp_log('find_local => found by original_post_id=' . $oid . ' => post_id=' . $fx[0]->ID); 
                return $fx[0]->ID; 
            }
        }
        $dup = get_post_by_title($title, $allowed);
        if ($dup) { 
            wprrp_log('find_local => found duplicate by title => post_id=' . $dup); 
            return $dup; 
        }
        wprrp_log('find_local => no match => creating new');
        return 0;
    }
    public function assign_tax($lid, $d, $pt) {
        wprrp_log('assign_tax => post_id=' . $lid . ' type=' . $pt);
        if ($pt === 'movie') {
            if (isset($d['categories']) && is_array($d['categories'])) {
                $xx = $this->import_terms($d['categories'], 'category');
                wp_set_post_terms($lid, $xx, 'category', false);
                wprrp_log('assign_tax => categories=' . print_r($xx, true));
            }
            if (isset($d['imdb'])) {
                wp_set_object_terms($lid, $d['imdb'], 'imdb', false);
                wprrp_log('assign_tax => imdb=' . $d['imdb']);
            }
            if (isset($d['year']) && is_array($d['year'])) {
                $ys = array();
                foreach ($d['year'] as $a) {
                    if (isset($a['name'])) {
                        $ys[] = sanitize_text_field($a['name']);
                    } elseif (is_string($a)) {
                        $ys[] = sanitize_text_field($a);
                    }
                }
                wp_set_object_terms($lid, $ys, 'years', false);
                wprrp_log('assign_tax => years=' . print_r($ys, true));
            }
        } elseif ($pt === 'serie' || $pt === 'anime') {
            if (isset($d['categories']) && is_array($d['categories'])) {
                $ct = $this->import_terms($d['categories'], 'category');
                wp_set_post_terms($lid, $ct, 'category', false);
                wprrp_log('assign_tax => categories=' . print_r($ct, true));
            }
            if (isset($d['imdb'])) {
                wp_set_object_terms($lid, $d['imdb'], 'imdb', false);
                wprrp_log('assign_tax => imdb=' . $d['imdb']);
            }
            if (isset($d['year']) && is_array($d['year'])) {
                $yr = array();
                foreach ($d['year'] as $z) {
                    if (isset($z['name'])) {
                        $yr[] = sanitize_text_field($z['name']);
                    } elseif (is_string($z)) {
                        $yr[] = sanitize_text_field($z);
                    }
                }
                wp_set_object_terms($lid, $yr, 'years', false);
                wprrp_log('assign_tax => years=' . print_r($yr, true));
            }
            if ($pt === 'serie' && isset($d['specific_categories'])) {
                $sc = $this->import_terms($d['specific_categories'], 'serie_category');
                wp_set_post_terms($lid, $sc, 'serie_category', false);
                wprrp_log('assign_tax => serie_category=' . print_r($sc, true));
            }
            if ($pt === 'anime' && isset($d['specific_categories'])) {
                $ac = $this->import_terms($d['specific_categories'], 'anime_category');
                wp_set_post_terms($lid, $ac, 'anime_category', false);
                wprrp_log('assign_tax => anime_category=' . print_r($ac, true));
            }
        } elseif ($pt === 'adult') {
            if (isset($d['adult_categories']) && is_array($d['adult_categories'])) {
                $ax = $this->import_terms($d['adult_categories'], 'adult_category');
                wp_set_post_terms($lid, $ax, 'adult_category', false);
                wprrp_log('assign_tax => adult_category=' . print_r($ax, true));
            }
        }
    }
    public function import_terms($arr, $tax) {
        wprrp_log('import_terms => tax=' . $tax . ' terms=' . print_r($arr, true));
        $ids = array();
        foreach ($arr as $a) {
            $a = sanitize_text_field($a);
            $tm = get_term_by('name', $a, $tax);
            if (!$tm) {
                $ins = wp_insert_term($a, $tax);
                if (!is_wp_error($ins)) {
                    $ids[] = $ins['term_id'];
                    wprrp_log('import_terms => created term=' . $a . ' id=' . $ins['term_id']);
                } else {
                    wprrp_log('import_terms error => ' . print_r($ins->get_error_messages(), true));
                }
            } else {
                $ids[] = $tm->term_id;
                wprrp_log('import_terms => existing term=' . $a . ' id=' . $tm->term_id);
            }
        }
        return $ids;
    }
    public function apply_ai($pid, $title, $oldc, $prov, $keys) {
        wprrp_log('apply_ai => post_id=' . $pid . ' prov=' . $prov . ' keys=' . print_r($keys, true));
        $old = get_post_field('post_content', $pid);
        $ai_result = $this->call_ai($title, $prov, $keys);
        if ($ai_result) {
            $new = $old . "\n\n" . $ai_result;
            wp_update_post(array('ID' => $pid, 'post_content' => $new));
            wprrp_log('apply_ai => updated post_content with AI text => post_id=' . $pid);
        } else {
            wprrp_log('apply_ai => no AI result or error => post_id=' . $pid);
        }
    }
    public function call_ai($title, $prov, $keys) {
        if (!$keys) {
            wprrp_log('call_ai => no keys => cannot proceed => provider=' . $prov);
            return '';
        }
        if ($prov === 'openai') {
            $api = $keys['openai'] ?? '';
            if (!$api) {
                wprrp_log('call_ai => missing openai key => cannot proceed');
                return '';
            }
            $cu = curl_init();
            $pl = array(
                'model' => 'gpt-3.5-turbo',
                'messages' => array(
                    array('role' => 'system', 'content' => 'Write SEO text for: ' . $title)
                )
            );
            curl_setopt($cu, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
            curl_setopt($cu, CURLOPT_POST, true);
            curl_setopt($cu, CURLOPT_POSTFIELDS, json_encode($pl));
            curl_setopt($cu, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Authorization: Bearer ' . $api));
            curl_setopt($cu, CURLOPT_RETURNTRANSFER, true);
            $res = curl_exec($cu);
            curl_close($cu);
            $j = @json_decode($res, true);
            if (isset($j['choices'][0]['message']['content'])) {
                wprrp_log('call_ai => openai success => got content');
                return wp_strip_all_tags($j['choices'][0]['message']['content']);
            }
            wprrp_log('call_ai => openai no content or error => ' . print_r($j, true));
        } elseif ($prov === 'deepseek') {
            $api = $keys['deepseek'] ?? '';
            if (!$api) {
                wprrp_log('call_ai => missing deepseek key => cannot proceed');
                return '';
            }
            $cx = curl_init();
            $pl = array(
                'model' => 'deepseek-chat',
                'messages' => array(
                    array('role' => 'system', 'content' => 'Write SEO text for: ' . $title)
                ),
                'stream' => false
            );
            curl_setopt($cx, CURLOPT_URL, 'https://api.deepseek.com/chat/completions');
            curl_setopt($cx, CURLOPT_POST, true);
            curl_setopt($cx, CURLOPT_POSTFIELDS, json_encode($pl));
            curl_setopt($cx, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Authorization: Bearer ' . $api));
            curl_setopt($cx, CURLOPT_RETURNTRANSFER, true);
            $res = curl_exec($cx);
            curl_close($cx);
            $j = @json_decode($res, true);
            if (isset($j['choices'][0]['message']['content'])) {
                wprrp_log('call_ai => deepseek success => got content');
                return wp_strip_all_tags($j['choices'][0]['message']['content']);
            }
            wprrp_log('call_ai => deepseek no content or error => ' . print_r($j, true));
        } else {
            wprrp_log('call_ai => unrecognized provider => ' . $prov);
        }
        return '';
    }
}

function wprrp_init() {
    WP_Post_Remote_Receiver_Pro::instance();
}
add_action('plugins_loaded', 'wprrp_init');
function wprrp_modify_main_query($query) {
    if ( ! is_admin() && $query->is_main_query() ) {
        $siteSettings = get_option('mytheme_site_settings', array());
        $allowed = ( isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types']) )
            ? $siteSettings['allowed_post_types'] : array();
        if ( ! in_array('page', $allowed) ) {
            $allowed[] = 'page';
        }
        if ( $query->is_home() ) {
            $exclude = array('page', 'post', 'adult');
            $allowed_home = array_values( array_diff($allowed, $exclude) );
            $query->set('post_type', $allowed_home);
        } else {
            $query->set('post_type', $allowed);
        }
    }
}
add_action('pre_get_posts', 'wprrp_modify_main_query');
function disable_default_post_admin_bar($wp_admin_bar) {
    $wp_admin_bar->remove_node('new-post');
}
add_action('admin_bar_menu', 'disable_default_post_admin_bar', 999);
function wprrp_activation() {
    $siteSettings = get_option('mytheme_site_settings', array());
    $allowed = isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types']) ? $siteSettings['allowed_post_types'] : array();
    if (in_array('movie', $allowed)) {
        custom_post_type_movie();
        custom_movieyear();
        custom_imdb();
        create_years_and_imdb_terms_on_plugin_activation();
    }
    if (in_array('serie', $allowed)) {
        custom_post_type_serie();
        custom_movieyear();
        custom_imdb();
        serie_custom_taxonomy();
        create_years_and_imdb_terms_on_plugin_activation();
    }
    if (in_array('anime', $allowed)) {
        custom_post_type_anime();
        anime_custom_taxonomy();
        custom_movieyear();
        custom_imdb();
    }
    if (in_array('adult', $allowed)) {
        custom_post_type_adult();
        adult_custom_taxonomy();
    }
    flush_rewrite_rules();
}
register_activation_hook(__FILE__, 'wprrp_activation');

function wprrp_deactivation() {
    flush_rewrite_rules();
}
register_deactivation_hook(__FILE__, 'wprrp_deactivation');

function purge_cloudflare_cache($input) {
    if (empty($input)) {
        return false;
    }
    $url_to_purge = is_numeric($input) ? get_permalink($input) : $input;
    if (!$url_to_purge) {
        return false;
    }
    if (preg_match('/\/page\/\d+/', $url_to_purge)) {
        return false;
    }
    $email = get_option('cf_email', '');
    $api_key = get_option('cf_api_key', '');
    $zone_id = get_option('cf_zone_id', '');
    if (empty($email) || empty($api_key) || empty($zone_id)) {
        return false;
    }
    $response = wp_remote_post('https://api.cloudflare.com/client/v4/zones/' . $zone_id . '/purge_cache', array(
        'headers' => array(
            'X-Auth-Email' => $email,
            'X-Auth-Key' => $api_key,
            'Content-Type' => 'application/json'
        ),
        'body' => json_encode(array('files' => array($url_to_purge)))
    ));
    if (is_wp_error($response)) {
        return false;
    }
    $body = wp_remote_retrieve_body($response);
    $decoded_response = json_decode($body, true);
    if (isset($decoded_response['success']) && $decoded_response['success'] === true) {
        return $decoded_response;
    }
    return false;
}
add_action('pre_post_update', 'capture_old_post_url', 10, 2);

function capture_old_post_url($post_id, $data) {
    $old_post_url = get_permalink($post_id);
    update_post_meta($post_id, '_old_post_url', $old_post_url);
}
add_action('save_post', 'purge_post_and_page_cloudflare_cache');

function purge_post_and_page_cloudflare_cache($post_id) {
    if (wp_is_post_revision($post_id) || get_post_status($post_id) === 'auto-draft') {
        return;
    }
    $home_url = home_url('/');
    if ($home_url) {
        purge_cloudflare_cache($home_url);
    }
    $old_post_url = get_post_meta($post_id, '_old_post_url', true);
    if ($old_post_url) {
        purge_cloudflare_cache($old_post_url);
        delete_post_meta($post_id, '_old_post_url');
    }
    $post_url = get_permalink($post_id);
    if ($post_url) {
        purge_cloudflare_cache($post_url);
    }
    $pages = get_pages();
    foreach ($pages as $page) {
        $page_url = get_permalink($page->ID);
        purge_cloudflare_cache($page_url);
    }
}

function update_post_slug($post_id) {
    if (wp_is_post_revision($post_id)) {
        return;
    }
    remove_action('save_post', 'update_post_slug');
    list($english_title, $thai_title, $year) = split_title($post_id);
    $new_slug = '';
    if (preg_match("/[a-zA-Z0-9]/", $english_title)) {
        $new_slug = sanitize_title($english_title . '-' . $year);
    } else if (!empty($thai_title)) {
        $new_slug = sanitize_title($thai_title . '-' . $year);
    }
    if (!empty($new_slug)) {
        wp_update_post(array(
            'ID' => $post_id,
            'post_name' => $new_slug
        ));
    }
}

function delete_attached_media($post_id) {
    $attachments = get_posts(array(
        'post_type' => 'attachment',
        'posts_per_page' => -1,
        'post_parent' => $post_id,
    ));
    if ($attachments) {
        foreach ($attachments as $attachment) {
            if (!wp_delete_attachment($attachment->ID, true)) {
                return false;
            }
        }
    }
    return true;
}
function encode_image_url($url) {
    $parts = parse_url($url);
    if (false === $parts) {
        return $url;
    }
    $scheme = isset($parts['scheme']) ? $parts['scheme'] . '://' : '';
    $host = $parts['host'] ?? '';
    $path = '';
    if (isset($parts['path'])) {
        $segments = explode('/', $parts['path']);
        foreach ($segments as &$segment) {
            $segment = rawurlencode($segment);
        }
        $path = implode('/', $segments);
    }
    $query = isset($parts['query']) ? '?' . $parts['query'] : '';
    $fragment = isset($parts['fragment']) ? '#' . $parts['fragment'] : '';
    return $scheme . $host . $path . $query . $fragment;
}

function download_and_set_featured_image($post_id, $image_url) {
    if (!delete_attached_media($post_id)) {
        return;
    }
    $encoded_url = encode_image_url($image_url);
    if (!filter_var($encoded_url, FILTER_VALIDATE_URL)) {
        return;
    }
    $image_response = wp_remote_get($encoded_url);
    if (is_wp_error($image_response)) {
        return;
    }
    $image_body = wp_remote_retrieve_body($image_response);
    if (empty($image_body)) {
        return;
    }
    list($english_title, $thai_title, $year) = split_title($post_id);
    $base_filename = $english_title ? $english_title : $thai_title;
    if (empty($base_filename)) {
        $base_filename = 'featured_image';
    }
    $filename = sanitize_file_name($base_filename) . '.webp';
    $upload = wp_upload_bits($filename, null, $image_body);
    if ($upload['error']) {
        return;
    }
    $image_editor = wp_get_image_editor($upload['file']);
    if (is_wp_error($image_editor)) {
        return;
    }
    $image_editor->resize(278, 420, true);
    $image_editor->set_quality(50);
    $saved_image = $image_editor->save($upload['file'], 'image/webp');
    if (is_wp_error($saved_image)) {
        return;
    }
    $filetype = wp_check_filetype($upload['file'], null);
    $attachment = array(
        'post_mime_type' => $filetype['type'],
        'post_title'     => sanitize_file_name($filename),
        'post_content'   => '',
        'post_status'    => 'inherit'
    );
    $attach_id = wp_insert_attachment($attachment, $upload['file'], $post_id);
    if (is_wp_error($attach_id)) {
        return;
    }
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    $attach_data = wp_generate_attachment_metadata($attach_id, $upload['file']);
    if (is_wp_error($attach_data)) {
        return;
    }
    wp_update_attachment_metadata($attach_id, $attach_data);
    set_post_thumbnail($post_id, $attach_id);
    update_post_meta($post_id, '_featured_image_url', $image_url);
}
function auto_set_featured_image_from_meta($post_id) {
    if (has_post_thumbnail($post_id)) {
        return;
    }
    $featured_image_url = get_post_meta($post_id, '_featured_image_url', true);
    if ($featured_image_url) {
        download_and_set_featured_image($post_id, $featured_image_url);
    }
}
$siteSettings = get_option('mytheme_site_settings', array());
$allowTpl = isset($siteSettings['allow_template_option']) ? $siteSettings['allow_template_option'] : '';
if ($allowTpl === 'yes') {
    add_action('admin_menu', 'mytheme_setup_menu');
    add_action('admin_enqueue_scripts', 'mytheme_admin_enqueue_scripts');
    add_action('wp_ajax_mytheme_ajax_save_options', 'mytheme_ajax_save_options');
    add_action('wp_ajax_mytheme_ajax_export', 'mytheme_ajax_export');
    add_action('wp_ajax_mytheme_ajax_clear_all', 'mytheme_ajax_clear_all');
    add_action('wp_ajax_mytheme_ajax_import', 'mytheme_ajax_import');
    add_action('admin_post_mytheme_download_logs', 'mytheme_download_logs');
    function mytheme_setup_menu() {
        add_menu_page('Theme Options', 'Theme Options', 'manage_options', 'mytheme-advanced-options', 'mytheme_options_page', 'dashicons-admin-generic', 59);
        add_submenu_page('mytheme-advanced-options', 'MyTheme Logs', 'MyTheme Logs', 'manage_options', 'mytheme-logs', 'mytheme_logs_page');
    }
    function mytheme_admin_enqueue_scripts($hook) {
        if ($hook === 'toplevel_page_mytheme-advanced-options' || $hook === 'mytheme-advanced-options_page_mytheme-logs') {
            wp_enqueue_media();
            wp_enqueue_script('jquery');
            wp_enqueue_script('sweetalert2', 'https://cdn.jsdelivr.net/npm/sweetalert2@11', array('jquery'), null, true);
            wp_enqueue_script('mytheme-admin-ajax', '', array('sweetalert2'), null, true);
            add_action('admin_print_footer_scripts', 'mytheme_admin_inline_script', 99);
        }
    }
    function mytheme_admin_inline_script() {
        ?>
        <script>
            (function($){
                $(document).ready(function(){
                    function switchTab(tabId){
                        $('.mytheme-nav-tab').removeClass('active');
                        $('.mytheme-content-tab').hide();
                        $('.mytheme-nav-tab[data-tab="'+tabId+'"]').addClass('active');
                        $('#'+tabId).show();
                    }
                    $('.mytheme-nav-tab').on('click',function(e){
                        e.preventDefault();
                        var t=$(this).data('tab');
                        switchTab(t);
                    });
                    switchTab($('.mytheme-nav-tab.active').data('tab'));
                    $('.mytheme-ajax-save').on('click',function(e){
                        e.preventDefault();
                        var data=$('#mythemeSingleForm').serializeArray();
                        var formData={};
                        data.forEach(function(d){formData[d.name]=d.value});
                        Swal.fire({
                            title:'Confirm Save?',
                            text:'No page refresh, proceed?',
                            icon:'question',
                            showCancelButton:true,
                            confirmButtonText:'Yes',
                            cancelButtonText:'Cancel'
                        }).then(function(res){
                            if(res.isConfirmed){
                                $.ajax({
                                    url:ajaxurl,
                                    type:'POST',
                                    dataType:'json',
                                    data:$.extend({action:'mytheme_ajax_save_options',mytheme_nonce:formData['mytheme_save_nonce']},formData),
                                    success:function(resp){
                                        if(resp.status==='ok'){
                                            Swal.fire('Saved','Options have been saved','success');
                                        }else{
                                            Swal.fire('Error',resp.message||'Unknown error','error');
                                        }
                                    },
                                    error:function(){
                                        Swal.fire('Error','AJAX Failed','error');
                                    }
                                });
                            }
                        });
                    });
                    $('#mytheme-ajax-export').on('click',function(e){
                        e.preventDefault();
                        Swal.fire({
                            title:'Exporting...',
                            html:'<div class="lds-dual-ring"></div>',
                            showConfirmButton:false,
                            allowOutsideClick:false,
                            allowEscapeKey:false
                        });
                        $.ajax({
                            url:ajaxurl,
                            type:'POST',
                            dataType:'json',
                            data:{action:'mytheme_ajax_export'},
                            success:function(resp){
                                if(resp.status==='ok'){
                                    var blob=new Blob([JSON.stringify(resp.data)],{type:'application/json'});
                                    var url=URL.createObjectURL(blob);
                                    Swal.fire({
                                        title:'Export Complete',
                                        icon:'success',
                                        showCancelButton:false,
                                        confirmButtonText:'Download'
                                    }).then(function(){
                                        var a=document.createElement('a');
                                        a.href=url;
                                        a.download='mytheme-options-'+(new Date()).getTime()+'.json';
                                        document.body.appendChild(a);
                                        a.click();
                                        document.body.removeChild(a);
                                    });
                                }else{
                                    Swal.fire('Error',resp.message||'Unknown error','error');
                                }
                            },
                            error:function(){
                                Swal.fire('Error','AJAX Failed','error');
                            }
                        });
                    });
                    $('#mytheme-clear-all').on('click',function(e){
                        e.preventDefault();
                        Swal.fire({
                            title:'Clear All Data?',
                            text:'This will remove all saved settings. Continue?',
                            icon:'warning',
                            showCancelButton:true,
                            confirmButtonText:'Yes',
                            cancelButtonText:'Cancel'
                        }).then(function(result){
                            if(result.isConfirmed){
                                $.ajax({
                                    url:ajaxurl,
                                    type:'POST',
                                    dataType:'json',
                                    data:{
                                        action:'mytheme_ajax_clear_all',
                                        mytheme_nonce:$('input[name="mytheme_save_nonce"]').val()
                                    },
                                    success:function(resp){
                                        if(resp.status==='ok'){
                                            Swal.fire('Cleared','All data has been cleared','success');
                                        }else{
                                            Swal.fire('Error',resp.message||'Unknown error','error');
                                        }
                                    },
                                    error:function(){
                                        Swal.fire('Error','AJAX Failed','error');
                                    }
                                });
                            }
                        });
                    });
                    $('#mytheme-ajax-import').on('click',function(e){
                        e.preventDefault();
                        var fileInput=document.getElementById('mytheme-import-json-file');
                        if(fileInput.files.length===0){
                            Swal.fire('Error','Please select a JSON file to import','error');
                            return;
                        }
                        var file=fileInput.files[0];
                        var formData=new FormData();
                        formData.append('action','mytheme_ajax_import');
                        formData.append('mytheme_import_nonce',$('#mytheme-import-nonce').val());
                        formData.append('mytheme_import_json',file);
                        Swal.fire({
                            title:'Importing...',
                            html:'<div class="lds-dual-ring"></div>',
                            showConfirmButton:false,
                            allowOutsideClick:false,
                            allowEscapeKey:false
                        });
                        $.ajax({
                            url:ajaxurl,
                            type:'POST',
                            data:formData,
                            processData:false,
                            contentType:false,
                            dataType:'json',
                            success:function(resp){
                                if(resp.status==='ok'){
                                    Swal.fire('Imported',resp.message,'success');
                                }else{
                                    Swal.fire('Error',resp.message||'Unknown error','error');
                                }
                            },
                            error:function(){
                                Swal.fire('Error','AJAX Failed','error');
                            }
                        });
                    });
                });
            })(jQuery);
        </script>
        <style>
            .lds-dual-ring{display:inline-block;width:64px;height:64px}
            .lds-dual-ring:after{content:" ";display:block;width:46px;height:46px;margin:1px;border-radius:50%;border:5px solid #fff;border-color:#fff transparent #fff transparent;animation:lds-dual-ring 1.2s linear infinite}
            @keyframes lds-dual-ring{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
        </style>
        <?php
    }
    function mytheme_options_page() {
        if (!current_user_can('manage_options')) return;
        $options = get_option('mytheme_options_data');
        if (!is_array($options)) $options = array();
        ?>
        <div class="wrap">
            <h1>MyTheme Advanced Options</h1>
            <style>
                .mytheme-nav-tab{display:inline-block;padding:8px 14px;margin-right:5px;color:#fff;background:#0073aa;text-decoration:none;border-radius:4px;cursor:pointer}
                .mytheme-nav-tab:hover{background:#005177}
                .mytheme-nav-tab.active{background:#23282d}
                .mytheme-content-tab{display:none;background:#fff;border-radius:8px;padding:20px;box-shadow:0 2px 4px rgba(0,0,0,0.1);margin-top:10px}
                .mytheme-field-group{margin-bottom:15px}
                .mytheme-label{display:block;font-weight:600;margin-bottom:5px}
                .mytheme-input,.mytheme-textarea,select{width:100%;padding:8px;border:1px solid #ccc;border-radius:4px;min-height:40px !important}
                .mytheme-textarea{min-height:140px !important}
                .mytheme-button{background:#0073aa;color:#fff;padding:8px 16px;border:none;border-radius:4px;cursor:pointer}
                .mytheme-button:hover{background:#005177}
                .mytheme-chips{display:flex;flex-wrap:wrap;gap:6px;margin-top:6px}
                .mytheme-chip{background:#f1f1f1;border-radius:20px;padding:5px 10px;display:flex;align-items:center}
                .mytheme-chip-remove{margin-left:5px;cursor:pointer;color:#c00;font-weight:bold}
                .menu-group{border:1px solid #ccc;padding:10px;margin-bottom:10px;border-radius:4px}
                .menu-group-header{display:flex;justify-content:space-between;align-items:center;padding:8px;background:#e6e6e6;border-radius:4px;cursor:pointer}
                .menu-group-header strong{margin:0;font-size:20px}
                .menu-items{display:none;width:100%;margin-top:10px}
                .menu-item-row{display:flex;gap:10px;margin-bottom:5px;width:100%}
            </style>
            <div>
                <span class="mytheme-nav-tab active" data-tab="tab_general">General</span>
                <span class="mytheme-nav-tab" data-tab="tab_menus">Menus</span>
                <span class="mytheme-nav-tab" data-tab="tab_import_export">Import/Export</span>
            </div>
            <form id="mythemeSingleForm">
                <?php wp_nonce_field('mytheme_save_action', 'mytheme_save_nonce'); ?>
                <div id="tab_general" class="mytheme-content-tab">
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Site Header</label>
                        <input type="text" name="site_header" class="mytheme-input" value="<?php echo isset($options['site_header']) ? esc_attr($options['site_header']) : ''; ?>">
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Site Logo</label>
                        <div style="display:flex;align-items:center;gap:10px;">
                            <input type="text" id="site_logo" name="site_logo" class="mytheme-input" style="width:70%;" value="<?php echo isset($options['site_logo']) ? esc_url($options['site_logo']) : ''; ?>">
                            <button type="button" class="mytheme-button" onclick="mytheme_open_media_uploader('site_logo')">Select Image</button>
                        </div>
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Site Icon</label>
                        <div style="display:flex;align-items:center;gap:10px;">
                            <input type="text" id="site_icon" name="site_icon" class="mytheme-input" style="width:70%;" value="<?php echo isset($options['site_icon']) ? esc_url($options['site_icon']) : ''; ?>">
                            <button type="button" class="mytheme-button" onclick="mytheme_open_media_uploader('site_icon')">Select Icon</button>
                        </div>
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Header Description</label>
                        <textarea name="header_description" rows="3" class="mytheme-textarea"><?php echo isset($options['header_description']) ? $options['header_description'] : ''; ?></textarea>
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Main Title</label>
                        <input type="text" name="main_title" class="mytheme-input" value="<?php echo isset($options['main_title']) ? esc_attr($options['main_title']) : ''; ?>">
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Reserve Title</label>
                        <input type="text" name="reserve_title" class="mytheme-input" value="<?php echo isset($options['reserve_title']) ? esc_attr($options['reserve_title']) : ''; ?>">
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Footer Description</label>
                        <textarea name="footer_description" rows="3" class="mytheme-textarea"><?php echo isset($options['footer_description']) ? $options['footer_description'] : ''; ?></textarea>
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Combined Title</label>
                        <input type="text" name="generated_combined_title" class="mytheme-input" value="<?php echo isset($options['generated_combined_title']) ? esc_attr($options['generated_combined_title']) : 'ผลการค้นหายอดนิยม'; ?>" maxlength="50">
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Number of Tags to Randomize</label>
                        <input type="number" name="generated_tags_count" id="generated_tags_count" class="mytheme-input" value="<?php echo isset($options['generated_tags_count']) ? intval($options['generated_tags_count']) : 50; ?>">
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Number of Categories to Randomize</label>
                        <input type="number" name="generated_categories_count" id="generated_categories_count" class="mytheme-input" value="<?php echo isset($options['generated_categories_count']) ? intval($options['generated_categories_count']) : 10; ?>">
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Generate Combined Keywords</label>
                        <input type="hidden" id="generated_combined_json" name="generated_combined_json" value="<?php echo isset($options['generated_combined']) ? esc_attr(json_encode($options['generated_combined'])) : ''; ?>">
                        <button type="button" class="mytheme-button" onclick="mytheme_generate_combined()">Generate</button>
                        <button type="button" class="mytheme-button" onclick="mytheme_add_manual_chip()">Add Manual</button>
                        <div id="mytheme-combined-chips-container" class="mytheme-chips"></div>
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Top Header Menu (Legacy)</label>
                        <select name="top_header_menu" class="mytheme-input">
                            <option value="">-- Select Menu --</option>
                            <?php
                            if (isset($options['custom_menus']) && is_array($options['custom_menus'])) {
                                foreach ($options['custom_menus'] as $menu) {
                                    ?>
                                    <option value="<?php echo esc_attr($menu['id']); ?>" <?php selected(isset($options['top_header_menu']) && $options['top_header_menu'] == $menu['id']); ?>><?php echo esc_html($menu['name']); ?></option>
                                    <?php
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Bottom Footer Menu (Legacy)</label>
                        <select name="bottom_footer_menu" class="mytheme-input">
                            <option value="">-- Select Menu --</option>
                            <?php
                            if (isset($options['custom_menus']) && is_array($options['custom_menus'])) {
                                foreach ($options['custom_menus'] as $menu) {
                                    ?>
                                    <option value="<?php echo esc_attr($menu['id']); ?>" <?php selected(isset($options['bottom_footer_menu']) && $options['bottom_footer_menu'] == $menu['id']); ?>><?php echo esc_html($menu['name']); ?></option>
                                    <?php
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="mytheme-field-group">
                        <label class="mytheme-label">Footer Text</label>
                        <input type="text" name="footer_text" class="mytheme-input" value="<?php echo isset($options['footer_text']) ? esc_attr($options['footer_text']) : ''; ?>">
                    </div>
                </div>
                <div id="tab_menus" class="mytheme-content-tab">
                    <input type="hidden" id="custom_menus_json" name="custom_menus_json" value="<?php echo isset($options['custom_menus']) ? esc_attr(json_encode($options['custom_menus'])) : ''; ?>">
                    <div style="margin-bottom:10px;">
                        <button type="button" class="mytheme-button" onclick="addMenuGroupSweet()">Add Menu Group</button>
                    </div>
                    <div id="menu-groups-container"></div>
                </div>
                <div id="tab_import_export" class="mytheme-content-tab">
                    <div style="margin-bottom:15px;">
                        <button id="mytheme-ajax-export" type="button" class="mytheme-button">Export JSON (Ajax)</button>
                    </div>
                    <div style="margin-bottom:15px;">
                        <input type="hidden" id="mytheme-import-nonce" value="<?php echo wp_create_nonce('mytheme_import_action'); ?>">
                        <input type="file" id="mytheme-import-json-file" accept=".json">
                        <button id="mytheme-ajax-import" type="button" class="mytheme-button">Import JSON (Ajax)</button>
                    </div>
                </div>
                <br>
                <button type="button" class="mytheme-button mytheme-ajax-save">Save All Tabs</button>
                <button type="button" id="mytheme-clear-all" class="mytheme-button" style="margin-left:10px;background:#cc0000;">Clear All</button>
            </form>
        </div>
        <script>
            window.mythemeAllTags = <?php echo json_encode(mytheme_get_top_tags()); ?>;
            window.mythemeAllCategories = <?php echo json_encode(mytheme_get_all_cats()); ?>;
            function mytheme_open_media_uploader(field_id){
                if(typeof wp==='undefined'||!wp.media){Swal.fire('Error','WordPress media library not available.','error');return}
                var frame=wp.media({title:'Select or Upload Logo',button:{text:'Use this image'},multiple:false});
                frame.on('select',function(){
                    var attachment=frame.state().get('selection').first().toJSON();
                    document.getElementById(field_id).value=attachment.url;
                });
                frame.open();
            }
            function mytheme_generate_combined(){
                var numTags = parseInt(document.getElementById('generated_tags_count').value) || 50;
                var numCats = parseInt(document.getElementById('generated_categories_count').value) || 10;
                var allTags = window.mythemeAllTags.slice();
                var allCats = window.mythemeAllCategories.slice();
                for(let i = allTags.length - 1; i > 0; i--){
                    const j = Math.floor(Math.random()*(i+1));
                    [allTags[i], allTags[j]] = [allTags[j], allTags[i]];
                }
                for(let i = allCats.length - 1; i > 0; i--){
                    const j = Math.floor(Math.random()*(i+1));
                    [allCats[i], allCats[j]] = [allCats[j], allCats[i]];
                }
                var selectedTags = allTags.slice(0, numTags).map(function(item){ return { slug: item.slug, name: item.name, taxonomy: 'post_tag', url: '' }; });
                var selectedCats = allCats.slice(0, numCats).map(function(item){ return { slug: item.slug, name: item.name, taxonomy: 'category', url: '' }; });
                var combined = selectedTags.concat(selectedCats);
                for(let i = combined.length - 1; i > 0; i--){
                    const j = Math.floor(Math.random()*(i+1));
                    [combined[i], combined[j]] = [combined[j], combined[i]];
                }
                document.getElementById('generated_combined_json').value = JSON.stringify(combined);
                mythemeRenderCombinedChips();
                Swal.fire('Generated','Combined keywords have been generated','success');
            }
            function mythemeRenderCombinedChips(){
                var container=document.getElementById('mytheme-combined-chips-container');
                if(!container)return;
                container.innerHTML='';
                var v=document.getElementById('generated_combined_json').value;
                if(!v||v==='')return;
                var arr=JSON.parse(v);
                arr.forEach(function(chip,index){
                    var chipElem=document.createElement('span');
                    chipElem.className='mytheme-chip';
                    var txt=document.createElement('span');
                    txt.textContent = chip.name ? chip.name : chip.text;
                    var rm=document.createElement('span');
                    rm.className='mytheme-chip-remove';
                    rm.textContent='×';
                    rm.style.cursor='pointer';
                    rm.style.marginLeft='5px';
                    rm.onclick=function(){
                        var current=JSON.parse(document.getElementById('generated_combined_json').value);
                        current.splice(index,1);
                        document.getElementById('generated_combined_json').value=JSON.stringify(current);
                        mythemeRenderCombinedChips();
                    };
                    chipElem.appendChild(txt);
                    chipElem.appendChild(rm);
                    container.appendChild(chipElem);
                });
            }
            function mytheme_add_manual_chip(){
                Swal.fire({
                    title:'Add Manual Keyword',
                    html:'<input id="swal-input1" class="swal2-input" placeholder="Text">' +
                         '<input id="swal-input2" class="swal2-input" placeholder="URL">',
                    focusConfirm:false,
                    preConfirm:() => {
                        return {
                            text: document.getElementById('swal-input1').value,
                            url: document.getElementById('swal-input2').value
                        }
                    }
                }).then((result)=>{
                    if(result.isConfirmed){
                        var data=result.value;
                        if(data.url.length>2000){
                            Swal.fire('Error','URL is too long','error');
                            return;
                        }
                        var current=[];
                        var existing=document.getElementById('generated_combined_json').value;
                        if(existing&&existing!==''){
                            current=JSON.parse(existing);
                        }
                        current.push({text:data.text,url:data.url,taxonomy:'manual'});
                        document.getElementById('generated_combined_json').value=JSON.stringify(current);
                        mythemeRenderCombinedChips();
                    }
                });
            }
            document.addEventListener('DOMContentLoaded',function(){
                mythemeRenderCombinedChips();
                renderMenuGroups();
            });
            function getMenuGroups(){
                var val=document.getElementById('custom_menus_json').value;
                if(!val||val==='')return[];
                try{return JSON.parse(val)}catch(e){return[]}
            }
            function setMenuGroups(data){
                document.getElementById('custom_menus_json').value=JSON.stringify(data);
            }
            function renderMenuGroups(){
                var container=document.getElementById('menu-groups-container');
                container.innerHTML='';
                var groups=getMenuGroups();
                groups.forEach(function(group,index){
                    var groupDiv=document.createElement('div');
                    groupDiv.className='menu-group';
                    var headerDiv=document.createElement('div');
                    headerDiv.className='menu-group-header';
                    headerDiv.onclick=function(e){
                        if(e.target.closest('.mytheme-button'))return;
                        var itemsDiv=groupDiv.querySelector('.menu-items');
                        if(itemsDiv.style.display==='none'||itemsDiv.style.display==='')itemsDiv.style.display='block';
                        else itemsDiv.style.display='none';
                    };
                    var title=document.createElement('strong');
                    title.textContent=group.name;
                    var removeBtn=document.createElement('button');
                    removeBtn.type='button';
                    removeBtn.className='mytheme-button';
                    removeBtn.textContent='Remove Group';
                    removeBtn.onclick=function(e){
                        e.stopPropagation();
                        Swal.fire({
                            title:'Remove Group?',
                            text:'Are you sure you want to remove this group?',
                            icon:'warning',
                            showCancelButton:true,
                            confirmButtonText:'Yes',
                            cancelButtonText:'Cancel'
                        }).then(function(r){
                            if(r.isConfirmed){
                                groups.splice(index,1);
                                setMenuGroups(groups);
                                renderMenuGroups();
                            }
                        });
                    };
                    headerDiv.appendChild(title);
                    headerDiv.appendChild(removeBtn);
                    groupDiv.appendChild(headerDiv);
                    var itemsDiv=document.createElement('div');
                    itemsDiv.className='menu-items';
                    itemsDiv.style.display='none';
                    if(group.items&&group.items.length){
                        group.items.forEach(function(item,itemIndex){
                            var itemDiv=document.createElement('div');
                            itemDiv.className='menu-item-row';
                            var labelInput=document.createElement('input');
                            labelInput.type='text';
                            labelInput.value=item.label;
                            labelInput.placeholder='Label';
                            labelInput.style.flex='1';
                            labelInput.onchange=function(){
                                groups[index].items[itemIndex].label=this.value;
                                setMenuGroups(groups);
                            };
                            var urlInput=document.createElement('input');
                            urlInput.type='text';
                            urlInput.value=item.url;
                            urlInput.placeholder='URL';
                            urlInput.style.flex='1';
                            urlInput.onchange=function(){
                                groups[index].items[itemIndex].url=this.value;
                                setMenuGroups(groups);
                            };
                            var removeItemBtn=document.createElement('button');
                            removeItemBtn.type='button';
                            removeItemBtn.className='mytheme-button';
                            removeItemBtn.textContent='Remove';
                            removeItemBtn.onclick=function(ev){
                                ev.preventDefault();
                                ev.stopPropagation();
                                groups[index].items.splice(itemIndex,1);
                                setMenuGroups(groups);
                                renderMenuGroups();
                                var groupElements=container.querySelectorAll('.menu-group');
                                if(groupElements[index]){
                                    var itemsDiv2=groupElements[index].querySelector('.menu-items');
                                    itemsDiv2.style.display='block';
                                }
                            };
                            itemDiv.appendChild(labelInput);
                            itemDiv.appendChild(urlInput);
                            itemDiv.appendChild(removeItemBtn);
                            itemsDiv.appendChild(itemDiv);
                        });
                    }
                    var addItemBtn=document.createElement('button');
                    addItemBtn.type='button';
                    addItemBtn.className='mytheme-button';
                    addItemBtn.textContent='Add Menu Item';
                    addItemBtn.onclick=function(e){
                        e.stopPropagation();
                        groups[index].items.push({label:"",url:""});
                        setMenuGroups(groups);
                        renderMenuGroups();
                        var groupElements=container.querySelectorAll('.menu-group');
                        if(groupElements[index]){
                            var itemsDiv2=groupElements[index].querySelector('.menu-items');
                            itemsDiv2.style.display='block';
                        }
                    };
                    itemsDiv.appendChild(addItemBtn);
                    groupDiv.appendChild(itemsDiv);
                    container.appendChild(groupDiv);
                });
            }
            function addMenuGroupSweet(){
                var groups=getMenuGroups();
                Swal.fire({
                    title:'Enter Group Name',
                    input:'text',
                    showCancelButton:true,
                    inputValidator:function(value){
                        if(!value){
                            return 'You need to write something!';
                        }
                    }
                }).then(function(result){
                    if(result.value){
                        var newGroup={id:'group_'+new Date().getTime(),name:result.value,items:[]};
                        groups.push(newGroup);
                        setMenuGroups(groups);
                        renderMenuGroups();
                    }
                });
            }
        </script>
        <?php
    }
    function mytheme_logs_page() {
        if (!current_user_can('manage_options')) return;
        $logfile = mytheme_get_log_file();
        $logcontent = '';
        if (file_exists($logfile)) $logcontent = file_get_contents($logfile);
        ?>
        <div class="wrap">
            <h1>MyTheme Logs</h1>
            <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                <textarea style="width:100%;height:300px;"><?php echo esc_textarea($logcontent); ?></textarea>
                <br><br>
                <input type="hidden" name="action" value="mytheme_download_logs">
                <button type="submit" class="button">Download Log File</button>
            </form>
        </div>
        <?php
    }
    function mytheme_ajax_save_options() {
        if (isset($_POST['log']) && $_POST['log'] === 'true') {
            mytheme_write_log(sanitize_text_field($_POST['message']));
            wp_die();
        }
        check_ajax_referer('mytheme_save_action', 'mytheme_nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json(array(
                'status' => 'error',
                'message' => 'Permission denied'
            ));
        }
        $options = get_option('mytheme_options_data');
        if (!is_array($options)) $options = array();
        $options['site_header'] = isset($_POST['site_header']) ? wp_kses_post($_POST['site_header']) : '';
        $options['site_logo'] = isset($_POST['site_logo']) ? esc_url_raw($_POST['site_logo']) : '';
        $options['site_icon'] = isset($_POST['site_icon']) ? esc_url_raw($_POST['site_icon']) : '';
        $options['header_description'] = isset($_POST['header_description']) ? wp_kses_post($_POST['header_description']) : '';
        $options['main_title'] = isset($_POST['main_title']) ? wp_kses_post($_POST['main_title']) : '';
        $options['reserve_title'] = isset($_POST['reserve_title']) ? wp_kses_post($_POST['reserve_title']) : '';
        $options['footer_description'] = isset($_POST['footer_description']) ? wp_kses_post($_POST['footer_description']) : '';
        $options['generated_combined_title'] = isset($_POST['generated_combined_title']) ? sanitize_text_field($_POST['generated_combined_title']) : 'ผลการค้นหายอดนิยม';
        $options['generated_tags_count'] = isset($_POST['generated_tags_count']) ? intval($_POST['generated_tags_count']) : 50;
        $options['generated_categories_count'] = isset($_POST['generated_categories_count']) ? intval($_POST['generated_categories_count']) : 10;
        $options['generated_combined'] = array();
        if (isset($_POST['generated_combined_json'])) {
            $decoded = json_decode(stripslashes($_POST['generated_combined_json']), true);
            if (is_array($decoded)) $options['generated_combined'] = $decoded;
        }
        $options['top_header_menu'] = isset($_POST['top_header_menu']) ? sanitize_text_field($_POST['top_header_menu']) : '';
        $options['bottom_footer_menu'] = isset($_POST['bottom_footer_menu']) ? sanitize_text_field($_POST['bottom_footer_menu']) : '';
        $options['footer_text'] = isset($_POST['footer_text']) ? wp_kses_post($_POST['footer_text']) : '';
        $options['custom_menus'] = array();
        if (isset($_POST['custom_menus_json'])) {
            $decoded = json_decode(stripslashes($_POST['custom_menus_json']), true);
            if (is_array($decoded)) $options['custom_menus'] = $decoded;
        }
	    update_option('mytheme_options_data', $options);
	    send_theme_options_to_receiver();
	    wp_send_json(array('status' => 'ok'));
	}
	function send_theme_options_to_receiver() {
	    $siteSettings = get_option('mytheme_site_settings', array());
	    $receiver_url = isset($siteSettings['sender_url']) ? $siteSettings['sender_url'] : '';
	    $token = isset($siteSettings['token']) ? $siteSettings['token'] : '';
	    $siteid = isset($siteSettings['site_id']) ? $siteSettings['site_id'] : '';
	    $theme_options = get_option('mytheme_options_data');
	    $endpoint = trailingslashit($receiver_url) . 'wp-json/mytheme/v1/receive-advanced-settings';
	    $args = array(
	        'timeout' => 10,
	        'body'    => wp_json_encode(array('advanced_settings' => $theme_options, 'site_id' => $siteid)),
	        'headers' => array(
	            'Content-Type'  => 'application/json',
	            'Authorization' => 'Bearer ' . $token
	        )
	    );
	    $r = wp_remote_post($endpoint, $args);
	    if (is_wp_error($r)) {
	        wprrp_log('Send theme options error: ' . $r->get_error_message());
	    } else {
	        wprrp_log('Send theme options success, code: ' . wp_remote_retrieve_response_code($r));
	    }
	}
    function mytheme_ajax_export() {
        if (!current_user_can('manage_options')) {
            wp_send_json(array(
                'status' => 'error',
                'message' => 'Permission denied'
            ));
        }
        $data = get_option('mytheme_options_data');
        if (!is_array($data)) $data = array();
        wp_send_json(array(
            'status' => 'ok',
            'data' => $data
        ));
    }
    function mytheme_ajax_clear_all() {
        check_ajax_referer('mytheme_save_action', 'mytheme_nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json(array(
                'status' => 'error',
                'message' => 'Permission denied'
            ));
        }
        update_option('mytheme_options_data', array());
        wp_send_json(array(
            'status' => 'ok'
        ));
    }
    function mytheme_ajax_import() {
        check_ajax_referer('mytheme_import_action', 'mytheme_import_nonce');
        if (!current_user_can('manage_options')) {
            wp_send_json(array(
                'status' => 'error',
                'message' => 'Permission denied'
            ));
        }
        if (empty($_FILES['mytheme_import_json']) || $_FILES['mytheme_import_json']['error'] !== 0) {
            wp_send_json(array(
                'status' => 'error',
                'message' => 'File upload error'
            ));
        }
        $json_content = file_get_contents($_FILES['mytheme_import_json']['tmp_name']);
        $data = json_decode($json_content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json(array(
                'status' => 'error',
                'message' => 'Invalid JSON'
            ));
        }
        update_option('mytheme_options_data', $data);
        wp_send_json(array(
            'status' => 'ok',
            'message' => 'Import successful'
        ));
    }
    function mytheme_download_logs() {
        if (!current_user_can('manage_options')) wp_die('No');
        $logfile = mytheme_get_log_file();
        if (!file_exists($logfile)) wp_die('Log file not found');
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="mytheme-log-' . date('YmdHis') . '.txt"');
        readfile($logfile);
        exit;
    }
    function mytheme_get_log_file() {
        $dir = wp_upload_dir();
        $logpath = $dir['basedir'] . '/mytheme-logs';
        if (!file_exists($logpath)) {
            @mkdir($logpath, 0755, true);
        }
        return $logpath . '/log.txt';
    }
    function mytheme_write_log($msg) {
        $file = mytheme_get_log_file();
        $line = '[' . date('Y-m-d H:i:s') . '] ' . $msg . "\n";
        @file_put_contents($file, $line, FILE_APPEND);
    }
    function mytheme_get_top_tags(){
        $tags = get_tags(array(
            'orderby' => 'count',
            'order' => 'DESC',
            'number' => 100
        ));
        $tag_list = array();
        if ($tags) {
            foreach ($tags as $tag) {
                $tag_list[] = array(
                    'slug' => $tag->slug,
                    'name' => $tag->name
                );
            }
        }
        return $tag_list;
    }
    function mytheme_get_all_cats(){
        $all_cats = get_categories(array(
            'hide_empty' => false
        ));
        $cat_list = array();
        foreach ($all_cats as $c) {
            $cat_list[] = array(
                'slug' => $c->slug,
                'name' => $c->name
            );
        }
        return $cat_list;
    }
}
function mytheme_all_options_shortcode() {
    ob_start();
    $options = get_option('mytheme_options_data', array());
    echo '<h3>Site Header: ' . (isset($options['site_header']) ? esc_html($options['site_header']) : '') . '</h3>';
    echo '<p>Main Title: ' . (isset($options['main_title']) ? esc_html($options['main_title']) : '') . '</p>';
    echo '<p>Reserve Title: ' . (isset($options['reserve_title']) ? esc_html($options['reserve_title']) : '') . '</p>';
    echo '<p>Header Description:</p>' . (isset($options['header_description']) ? $options['header_description'] : '');
    echo '<p>Footer Description:</p>' . (isset($options['footer_description']) ? $options['footer_description'] : '');
    echo '<p>Footer Text:</p>' . (isset($options['footer_text']) ? $options['footer_text'] : '');
    echo '<p>Site Logo: ';
    if (isset($options['site_logo']) && $options['site_logo'] !== '') {
        echo '<img src="' . esc_url($options['site_logo']) . '" style="max-width:150px;height:auto;" alt="Site Logo">';
    }
    echo '</p>';
    echo '<p>Top Header Menu (ID): ' . (isset($options['top_header_menu']) ? esc_html($options['top_header_menu']) : '') . '</p>';
    echo '<p>Bottom Footer Menu (ID): ' . (isset($options['bottom_footer_menu']) ? esc_html($options['bottom_footer_menu']) : '') . '</p>';
    if (isset($options['generated_combined']) && is_array($options['generated_combined']) && count($options['generated_combined']) > 0) {
        echo '<p>Combined Keywords:</p>';
        foreach ($options['generated_combined'] as $item) {
            if (isset($item['taxonomy']) && $item['taxonomy'] === 'manual') {
                $link = isset($item['url']) ? $item['url'] : '#';
                $text = isset($item['text']) ? $item['text'] : '';
                echo '<a href="' . esc_url($link) . '">' . esc_html($text) . '</a>';
            } else {
                $slug = isset($item['slug']) ? $item['slug'] : '';
                $manual_url = isset($item['url']) ? $item['url'] : '';
                if (!$slug) continue;
                if (isset($item['taxonomy']) && $item['taxonomy'] === 'post_tag') {
                    $term = get_term_by('slug', $slug, 'post_tag');
                    if ($term) {
                        $link = $manual_url ? $manual_url : get_tag_link($term->term_id);
                        echo '<a href="' . esc_url($link) . '">' . esc_html($term->name) . '</a>';
                    }
                } elseif (isset($item['taxonomy']) && $item['taxonomy'] === 'category') {
                    $cat = get_category_by_slug($slug);
                    if ($cat) {
                        $link = $manual_url ? $manual_url : get_category_link($cat->term_id);
                        echo '<a href="' . esc_url($link) . '">' . esc_html($cat->name) . '</a>';
                    }
                }
            }
        }
    }
    if (isset($options['custom_menus']) && is_array($options['custom_menus'])) {
        echo '<h3>Custom Menus</h3>';
        foreach ($options['custom_menus'] as $group) {
            echo '<h4>Group Name: ' . esc_html($group['name']) . '</h4>';
            echo '<p>Group ID: ' . esc_html($group['id']) . '</p>';
            if (isset($group['items']) && is_array($group['items'])) {
                echo '<ul>';
                foreach ($group['items'] as $item) {
                    echo '<li><a href="' . esc_url($item['url']) . '">' . esc_html($item['label']) . '</a></li>';
                }
                echo '</ul>';
            }
        }
    }
    return ob_get_clean();
}
add_shortcode('mytheme_all_options', 'mytheme_all_options_shortcode');
function mytheme_render_menu_by_selected($option_key) {
    $options = get_option('mytheme_options_data', array());
    $menu_id = isset($options[$option_key]) ? $options[$option_key] : '';
    if (!$menu_id) return;
    if (isset($options['custom_menus']) && is_array($options['custom_menus'])) {
        foreach ($options['custom_menus'] as $group) {
            if (isset($group['id']) && $group['id'] == $menu_id) {
                if (isset($group['items']) && is_array($group['items'])) {
                    echo '<ul>';
                    foreach ($group['items'] as $item) {
                        echo '<li><a href="' . esc_url($item['url']) . '">' . esc_html($item['label']) . '</a></li>';
                    }
                    echo '</ul>';
                }
            }
        }
    }
}
function mytheme_site_header_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['site_header']) ? $o['site_header'] : '';
    return $v;
}
add_shortcode('mytheme_site_header', 'mytheme_site_header_shortcode');
function mytheme_site_logo_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['site_logo']) ? $o['site_logo'] : '';
    if ($v !== '') {
        return '<img class="nl-lazy nl-lazy-bg show" src="' . esc_url($v) . '" alt="ดูหนังออนไลน์">';
    }
    return '';
}
add_shortcode('mytheme_site_logo', 'mytheme_site_logo_shortcode');
function mytheme_header_description_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['header_description']) ? $o['header_description'] : '';
    return $v;
}
add_shortcode('mytheme_header_description', 'mytheme_header_description_shortcode');
function mytheme_main_title_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['main_title']) ? $o['main_title'] : '';
    return $v;
}
add_shortcode('mytheme_main_title', 'mytheme_main_title_shortcode');
function mytheme_reserve_title_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['reserve_title']) ? $o['reserve_title'] : '';
    return $v;
}
add_shortcode('mytheme_reserve_title', 'mytheme_reserve_title_shortcode');
function mytheme_footer_description_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['footer_description']) ? $o['footer_description'] : '';
    return $v;
}
add_shortcode('mytheme_footer_description', 'mytheme_footer_description_shortcode');
function mytheme_footer_text_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['footer_text']) ? $o['footer_text'] : '';
    return $v;
}
add_shortcode('mytheme_footer_text', 'mytheme_footer_text_shortcode');
function mytheme_top_header_menu_id_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['top_header_menu']) ? $o['top_header_menu'] : '';
    return $v;
}
add_shortcode('mytheme_top_header_menu_id', 'mytheme_top_header_menu_id_shortcode');
function mytheme_bottom_footer_menu_id_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['bottom_footer_menu']) ? $o['bottom_footer_menu'] : '';
    return $v;
}
add_shortcode('mytheme_bottom_footer_menu_id', 'mytheme_bottom_footer_menu_id_shortcode');
function mytheme_generated_tags_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $html = '';
    $title = isset($o['generated_combined_title']) ? $o['generated_combined_title'] : 'ผลการค้นหายอดนิยม';
    $html .= '<p class="title-t">' . esc_html($title) . '</p>';
    if (isset($o['generated_combined']) && is_array($o['generated_combined']) && count($o['generated_combined']) > 0) {
        foreach ($o['generated_combined'] as $item) {
            if (isset($item['taxonomy']) && $item['taxonomy'] === 'manual') {
                $link = isset($item['url']) ? $item['url'] : '#';
                $text = isset($item['text']) ? $item['text'] : '';
                $html .= '<a href="' . esc_url($link) . '">' . esc_html($text) . '</a>';
            } else {
                $slug = isset($item['slug']) ? $item['slug'] : '';
                $manual_url = isset($item['url']) ? $item['url'] : '';
                if (!$slug) continue;
                if (isset($item['taxonomy']) && $item['taxonomy'] === 'post_tag') {
                    $term = get_term_by('slug', $slug, 'post_tag');
                    if ($term) {
                        $link = $manual_url ? $manual_url : get_tag_link($term->term_id);
                        $html .= '<a href="' . esc_url($link) . '">' . esc_html($term->name) . '</a>';
                    }
                } elseif (isset($item['taxonomy']) && $item['taxonomy'] === 'category') {
                    $cat = get_category_by_slug($slug);
                    if ($cat) {
                        $link = $manual_url ? $manual_url : get_category_link($cat->term_id);
                        $html .= '<a href="' . esc_url($link) . '">' . esc_html($cat->name) . '</a>';
                    }
                }
            }
        }
    } else {
        $tags = get_tags(array('orderby' => 'count', 'order' => 'DESC', 'number' => 100));
        $cats = get_categories(array('hide_empty' => false));
        if ($tags || $cats) {
            $combined = array();
            if ($tags) {
                foreach ($tags as $tag) {
                    $combined[] = array('slug' => $tag->slug, 'name' => $tag->name, 'taxonomy' => 'post_tag');
                }
            }
            if ($cats) {
                foreach ($cats as $cat) {
                    $combined[] = array('slug' => $cat->slug, 'name' => $cat->name, 'taxonomy' => 'category');
                }
            }
            shuffle($combined);
            $numTags = isset($o['generated_tags_count']) ? intval($o['generated_tags_count']) : 50;
            $numCats = isset($o['generated_categories_count']) ? intval($o['generated_categories_count']) : 10;
            $count = $numTags + $numCats;
            $combined = array_slice($combined, 0, $count);
            foreach ($combined as $item) {
                if (isset($item['taxonomy']) && $item['taxonomy'] === 'post_tag') {
                    $term = get_term_by('slug', $item['slug'], 'post_tag');
                    if ($term) {
                        $html .= '<a href="' . esc_url(get_tag_link($term->term_id)) . '">' . esc_html($term->name) . '</a>';
                    }
                } elseif (isset($item['taxonomy']) && $item['taxonomy'] === 'category') {
                    $cat = get_category_by_slug($item['slug']);
                    if ($cat) {
                        $html .= '<a href="' . esc_url(get_category_link($cat->term_id)) . '">' . esc_html($cat->name) . '</a>';
                    }
                }
            }
        }
    }
    return $html;
}
add_shortcode('mytheme_generated_tags', 'mytheme_generated_tags_shortcode');
function mytheme_header_menu_shortcode() {
    ob_start();
    $o = get_option('mytheme_options_data', array());
    $m = isset($o['top_header_menu']) ? $o['top_header_menu'] : '';
    if ($m && isset($o['custom_menus']) && is_array($o['custom_menus'])) {
        foreach ($o['custom_menus'] as $g) {
            if (isset($g['id']) && $g['id'] == $m) {
                echo '<nav id="site-navigation" class="main-navigation"><div class="menu-menu-container"><ul id="menu-mainmenu" class="menu">';
                if (isset($g['items']) && is_array($g['items'])) {
                    $i = 0;
                    foreach ($g['items'] as $it) {
                        echo '<li id="menu-item-' . $i . '" class="menu-item menu-item-' . $i . '"><a href="' . esc_url($it['url']) . '">' . esc_html($it['label']) . '</a></li>';
                        $i++;
                    }
                }
                echo '</ul></div></nav>';
                break;
            }
        }
    }
    return ob_get_clean();
}
add_shortcode('mytheme_header_menu', 'mytheme_header_menu_shortcode');
function mytheme_footer_menu_shortcode() {
    ob_start();
    $o = get_option('mytheme_options_data', array());
    $m = isset($o['bottom_footer_menu']) ? $o['bottom_footer_menu'] : '';
    if ($m && isset($o['custom_menus']) && is_array($o['custom_menus'])) {
        foreach ($o['custom_menus'] as $g) {
            if (isset($g['id']) && $g['id'] == $m) {
                echo '<nav id="site-navigation" class="main-navigation"><div class="menu-footer-container"><ul id="menu-footer" class="menu">';
                if (isset($g['items']) && is_array($g['items'])) {
                    $i = 0;
                    foreach ($g['items'] as $it) {
                        echo '<li id="menu-item-' . $i . '" class="menu-item"><a href="' . esc_url($it['url']) . '">' . esc_html($it['label']) . '</a></li>';
                        $i++;
                    }
                }
                echo '</ul></div></nav>';
                break;
            }
        }
    }
    return ob_get_clean();
}
add_shortcode('mytheme_footer_menu', 'mytheme_footer_menu_shortcode');
function mytheme_icon_shortcode() {
    $o = get_option('mytheme_options_data', array());
    $v = isset($o['site_icon']) ? $o['site_icon'] : '';
    if ($v !== '') {
        return '<link rel="icon" href="' . esc_url($v) . '" type="image/x-icon">';
    }
    return '';
}
add_shortcode('mytheme_icon', 'mytheme_icon_shortcode');
function disable_autosave_and_drafts() {
    wp_deregister_script('autosave');
}
add_action('admin_print_scripts-post-new.php', 'disable_autosave_and_drafts');
add_action('admin_print_scripts-post.php', 'disable_autosave_and_drafts');
add_filter('should_autosave', '__return_false');
function disable_autosave_scripts() {
    wp_deregister_script('autosave');
}
add_action('admin_enqueue_scripts', 'disable_autosave_scripts', 1);

function split_title($post_id) {
    $post = get_post($post_id);
    if (!$post) return array("", "", "");
    $title = $post->post_title;
    $title = preg_replace('/[-:?!""–]/u', '', $title);
    $year = "";
    if (preg_match('/\((\d{4})\)/', $title, $matches)) {
        $year = $matches[1];
        $title = str_replace($matches[0], "", $title);
    }
    $patterns = ['curly' => '/{(.*?)}/', 'square' => '/\[(.*?)\]/', 'parenthesis' => '/\((.*?)\)/'];
    $suffix_parts = [];
    $base_title = $title;
    foreach ($patterns as $pattern) {
        if (preg_match_all($pattern, $title, $matches)) {
            foreach ($matches[1] as $match) {
                $suffix_parts[] = $match;
                $base_title = str_replace($matches[0], "", $base_title);
            }
        }
    }
    $base_title = trim($base_title);
    $words = explode(" ", $base_title);
    $english_title = '';
    $thai_title = '';
    $seen_numbers = [];
    $seen_single_letters = [];
    foreach ($words as $word) {
        if (preg_match('/(\d+[\.\d+]*)/', $word, $num)) {
            if (in_array($num[1], $seen_numbers)) {
                $word = preg_replace('/\d+[\.\d+]*/', '', $word);
            } else {
                $seen_numbers[] = $num[1];
            }
        }
        if (preg_match('/[a-zA-Z0-9\']/u', $word)) {
            if (strlen($word) == 1 && !in_array($word, ['A', 'I']) && in_array($word, $seen_single_letters)) {
                continue;
            } else {
                $english_title .= $word . ' ';
                if (strlen($word) == 1) {
                    $seen_single_letters[] = $word;
                }
            }
        } else {
            $thai_title .= $word . ' ';
        }
    }
    foreach ($suffix_parts as $part) {
        if (preg_match_all('/\d+[\.\d+]*/', $part, $matches)) {
            foreach ($matches[0] as $num) {
                if (in_array($num, $seen_numbers)) {
                    $part = str_replace($num, '', $part);
                } else {
                    $seen_numbers[] = $num;
                }
            }
        }
        if (preg_match('/[a-zA-Z0-9\']/u', $part)) {
            if (strlen($part) == 1 && !in_array($part, ['A', 'I']) && in_array($part, $seen_single_letters)) {
                continue;
            } else {
                $english_title .= $part . ' ';
                if (strlen($part) == 1) {
                    $seen_single_letters[] = $part;
                }
            }
        } else {
            $thai_title .= $part . ' ';
        }
    }
    $english_title = trim($english_title);
    $thai_title = trim($thai_title);
    return array($english_title, $thai_title, $year);
}

add_action('wp_ajax_get_video_url', 'handle_get_video_url');
add_action('wp_ajax_nopriv_get_video_url', 'handle_get_video_url');

add_action('wp_ajax_test_video_url', 'handle_test_video_url');
add_action('wp_ajax_nopriv_test_video_url', 'handle_test_video_url');

function handle_test_video_url() {
    wprrp_log('Test AJAX called');
    wp_send_json_success(array('message' => 'AJAX is working', 'time' => current_time('mysql')));
}

function handle_get_video_url() {
    try {
        wprrp_log('handle_get_video_url called');

        $video_id = isset($_POST['video_id']) ? sanitize_text_field($_POST['video_id']) : '';
        $nonce = isset($_POST['nonce']) ? sanitize_text_field($_POST['nonce']) : '';

        wprrp_log('video_id: ' . $video_id);
        wprrp_log('nonce: ' . substr($nonce, 0, 20) . '...');

        if (empty($video_id)) {
            wprrp_log('Error: Video ID is empty');
            wp_send_json_error(array('message' => 'Video ID is required'));
            return;
        }

        if (empty($nonce)) {
            wprrp_log('Error: Nonce is empty');
            wp_send_json_error(array('message' => 'Security token is required'));
            return;
        }

        if (!validate_custom_token($nonce)) {
            wprrp_log('Error: Invalid token');
            wp_send_json_error(array('message' => 'Invalid security token'));
            return;
        }

        wprrp_log('Token validated successfully');

        $video_url = get_video_url_by_md5($video_id);

        wprrp_log('Video URL result: ' . ($video_url ? $video_url : 'not found'));

        if (!$video_url) {
            wp_send_json_error(array('message' => 'Video not found'));
            return;
        }

        wp_send_json_success(array('url' => $video_url));

    } catch (Exception $e) {
        wprrp_log('Exception in handle_get_video_url: ' . $e->getMessage());
        wp_send_json_error(array('message' => 'Server error: ' . $e->getMessage()));
    }
}

function validate_custom_token($token) {
    $secretKey = "m6mljIIxd0MIz8P6W3KK60v1dFyXzllATWtQX6yUCpu5UEDmwRAE2jrQRyfvF";
    
    if (empty($token) || strlen($token) < 32) {
        return false;
    }
    
    $decoded = hex2bin($token);
    if ($decoded === false) {
        return false;
    }
    
    $parts = explode(':', $decoded, 3);
    if (count($parts) !== 3) {
        return false;
    }
    
    $r = $parts[0];
    $exp = intval($parts[1]);
    $h = $parts[2];
    
    if ($exp < time()) {
        return false;
    }
    
    $raw = $r . ":" . $exp;
    $expected_hash = hash_hmac("sha256", $raw, $secretKey);
    
    return hash_equals($expected_hash, $h);
}

function get_video_url_by_md5($md5_id) {
    $post_types = array('movie', 'serie', 'anime', 'adult');
    $meta_keys = array('gdrivethai', 'gdrivesub', 'serie_dubbed', 'serie_subbed', 'adult_drive_link');
    foreach ($post_types as $post_type) {
        $posts = get_posts(array(
            'post_type' => $post_type,
            'post_status' => 'publish',
            'numberposts' => -1,
            'meta_query' => array(
                'relation' => 'OR'
            )
        ));
        foreach ($posts as $post) {
            foreach ($meta_keys as $meta_key) {
                $meta_value = get_post_meta($post->ID, $meta_key, true);
                if (empty($meta_value)) {
                    continue;
                }               
                if (is_string($meta_value)) {
                    if (md5($meta_value) === $md5_id) {
                        wprrp_log('Found matching URL (string): ' . $meta_value);
                        return $meta_value;
                    }
                } elseif (is_array($meta_value)) {
                    if (isset($meta_value['md5_id']) && $meta_value['md5_id'] === $md5_id) {
                        return $meta_value['url'];
                    }

                    if (isset($meta_value['url']) && is_string($meta_value['url']) && md5($meta_value['url']) === $md5_id) {
                        return $meta_value['url'];
                    }
                    foreach ($meta_value as $item) {
                        if (is_string($item) && md5($item) === $md5_id) {
                            return $item;
                        } elseif (is_array($item)) {
                            if (isset($item['md5_id']) && $item['md5_id'] === $md5_id) {
                                return $item['url'];
                            }
                            if (isset($item['url']) && is_string($item['url']) && md5($item['url']) === $md5_id) {
                                return $item['url'];
                            }
                        }
                    }
                }
            }
        }
    }
    return false;
}



