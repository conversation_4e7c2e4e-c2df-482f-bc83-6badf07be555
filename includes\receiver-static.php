<?php
if (!defined('ABSPATH')) exit;
function wprrp_activate() {
    flush_rewrite_rules();
}
function wprrp_log($msg) {
    if (!get_option('wprrp_enable_logging', 1)) return;
    $file = WP_CONTENT_DIR . '/wprrp_debug.log';
    $maxSize = 10485760;
    $entry = date('Y-m-d H:i:s') . ' - ' . $msg . PHP_EOL;
    if (!file_exists($file)) {
        touch($file);
    }
    if (filesize($file) >= $maxSize) {
        $handle = fopen($file, 'w');
        fclose($handle);
    }
    file_put_contents($file, $entry, FILE_APPEND | LOCK_EX);
}
function wprrp_format($data) {
    if (is_array($data) || is_object($data)) {
        return PHP_EOL . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    return (string)$data;
}
function nung_custom_menu_order($menu_ord) {
    if (!$menu_ord) return true;
    return array(
        'index.php',
        'edit.php?post_type=movie',
        'edit.php?post_type=serie',
        'edit.php?post_type=anime',
        'edit.php?post_type=adult'
    );
}
add_filter('custom_menu_order','nung_custom_menu_order');
add_filter('menu_order','nung_custom_menu_order');
add_action('init','wprrp_static_rewrite');
function wprrp_static_rewrite() {
    add_rewrite_rule('^receive-post/?$','index.php?receive_post=1','top');
    add_rewrite_tag('%receive_post%','1');
    add_rewrite_rule('^ping/?$','index.php?ping_test=1','top');
    add_rewrite_tag('%ping_test%','1');
    add_rewrite_rule('^push-code/?$','index.php?receiver_push_code=1','top');
    add_rewrite_tag('%receiver_push_code%','1');
    add_rewrite_rule('^receive-site-settings/?$','index.php?receive_site_settings=1','top');
    add_rewrite_tag('%receive_site_settings%','1');
    add_rewrite_rule('^receive-advanced-settings/?$','index.php?receive_advanced_settings=1','top');
    add_rewrite_tag('%receive_advanced_settings%','1');
    add_rewrite_rule('^player/([^/]+)/?$','index.php?player_video=1&video_id=$matches[1]','top');
    add_rewrite_tag('%player_video%','1');
}
add_action('parse_request','wprrp_static_parse_request');

function wprrp_static_parse_request($wp) {
    if (isset($wp->query_vars['player_video'])) {
        $video_id = isset($wp->query_vars['video_id']) ? sanitize_text_field($wp->query_vars['video_id']) : '';

        if (empty($video_id)) {
            wp_die('Invalid video ID');
        }
        $player_file = ABSPATH . 'player/index.php';
        if (file_exists($player_file)) {
            $_SERVER['REQUEST_URI'] = '/player/' . $video_id . '/';
            include $player_file;
            exit;
        } else {
            wp_die('Player template not found');
        }
    }
    if (isset($wp->query_vars['receive_post'])) {
        $m = $_SERVER['REQUEST_METHOD'] ?? '';
        wprrp_log('receive_post method: ' . $m);
        if ($m !== 'POST') {
            wprrp_log('receive_post invalid method');
            header('HTTP/1.1 403 Forbidden');
            echo 'INVALID';
            exit;
        }
        $raw = @file_get_contents('php://input');
        wprrp_log('receive_post raw: ' . wprrp_format($raw));
        $data = @json_decode($raw, true);
        wprrp_log('receive_post decoded: ' . wprrp_format($data));
        if (!$data) {
            wprrp_log('receive_post decode fail');
            header('HTTP/1.1 400 Bad Request');
            echo 'FAILED';
            exit;
        }
        $auth = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        $stored = get_option('wprrp_receiver_token', '');
        if (!$stored || $auth !== $stored) {
            wprrp_log('receive_post token mismatch');
            header('HTTP/1.1 403 Forbidden');
            echo 'INVALID';
            exit;
        }
        $pt = $data['post_type'] ?? '';
        $siteSettings = get_option('mytheme_site_settings', array());
        $allowed = isset($siteSettings['allowed_post_types']) && is_array($siteSettings['allowed_post_types']) ? $siteSettings['allowed_post_types'] : array();
        wprrp_log('receive_post allowed_post_types from settings: ' . wprrp_format($allowed));
        if (empty($allowed)) {
            wprrp_log('receive_post no allowed post types configured');
            header('HTTP/1.1 400 Bad Request');
            echo 'FAILED';
            exit;
        }
        if (!in_array($pt, $allowed)) {
            wprrp_log('receive_post invalid post_type: ' . $pt . ' not in allowed: ' . implode(', ', $allowed));
            header('HTTP/1.1 400 Bad Request');
            echo 'FAILED';
            exit;
        }
        WP_Post_Remote_Receiver_Pro::instance()->process_incoming($data);
        wprrp_log('receive_post done');
        header('HTTP/1.1 200 OK');
        echo 'SUCCESS';
        exit;
    }
    if (isset($wp->query_vars['ping_test'])) {
        $request_data = array('method'=>$_SERVER['REQUEST_METHOD'] ?? 'unknown','query_vars'=>$wp->query_vars);
        wprrp_log('ping_test: Request received: ' . wprrp_format($request_data));
        header('HTTP/1.1 200 OK');
        echo 'PONG';
        wprrp_log('ping_test: Response sent: PONG');
        exit;
    }
    if (isset($wp->query_vars['receiver_push_code'])) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('HTTP/1.1 405 Method Not Allowed');
            echo 'INVALID';
            exit;
        }
        $auth = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        $stored = get_option('wprrp_receiver_token','');
        if (!$stored || $auth !== $stored) {
            header('HTTP/1.1 403 Forbidden');
            echo 'FORBIDDEN';
            exit;
        }
        $raw = @file_get_contents('php://input');
        $data = @json_decode($raw,true);
        if (!$data || empty($data['code'])) {
            header('HTTP/1.1 400 Bad Request');
            echo json_encode(array('error'=>'No code received'));
            exit;
        }
        $file_type = isset($data['file_type']) ? sanitize_text_field($data['file_type']) : 'receiver';
        $plugin_root = dirname(dirname(__FILE__));
        if ($file_type === 'header') {
            $target_file = $plugin_root . '/wp-post-remote-receiver-pro.php';
        } elseif ($file_type === 'receiver') {
            $target_file = $plugin_root . '/includes/receiver-dynamic.php';
        } else {
            header('HTTP/1.1 400 Bad Request');
            echo json_encode(array('error'=>'Invalid file type'));
            exit;
        }
        $result = @file_put_contents($target_file,$data['code']);
        header('Content-Type: application/json; charset=UTF-8');
        if ($result !== false) {
            if ($file_type === 'header') {
                $content = file_get_contents($target_file);
                if (preg_match('/Version:\s*([^\s]+)/i',$content,$matches)) {
                    $version = $matches[1];
                } else {
                    $version = 'unknown';
                }
            } else {
                $version = 'v-wp-editor';
            }
            wprrp_log('push_code file updated: ' . $target_file . ' version: ' . $version);
            echo json_encode(array('version'=>$version,'message'=>'File updated'));
        } else {
            wprrp_log('push_code failed to update file: ' . $target_file);
            echo json_encode(array('error'=>'Failed to update code'));
        }
        exit;
    }
    if (isset($wp->query_vars['receive_site_settings'])) {
        $m = $_SERVER['REQUEST_METHOD'] ?? '';
        if ($m !== 'POST') {
            wprrp_log('receive_site_settings invalid method');
            header('HTTP/1.1 403 Forbidden');
            echo 'INVALID';
            exit;
        }
        $raw = @file_get_contents('php://input');
        wprrp_log('receive_site_settings raw data: ' . wprrp_format($raw));
        $data = @json_decode($raw,true);
        wprrp_log('receive_site_settings decoded: '.wprrp_format($data));
        if (!$data) {
            wprrp_log('receive_site_settings decode fail');
            header('HTTP/1.1 400 Bad Request');
            echo 'FAILED';
            exit;
        }
        $auth = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (strpos($auth,'Bearer ')===0) {
            $auth = substr($auth,7);
        }
        $stored = get_option('wprrp_receiver_token','');
        if (!$stored || $auth !== $stored) {
            wprrp_log('receive_site_settings token mismatch');
            header('HTTP/1.1 403 Forbidden');
            echo 'INVALID';
            exit;
        }
        if (!isset($data['site_settings']) || !is_array($data['site_settings'])) {
            wprrp_log('receive_site_settings invalid structure');
            header('HTTP/1.1 400 Bad Request');
            echo 'FAILED';
            exit;
        }
        update_option('mytheme_site_settings',$data['site_settings']);
        if (!empty($data['site_settings']['assigned_templates']) && is_array($data['site_settings']['assigned_templates'])) {
            foreach ($data['site_settings']['assigned_templates'] as $tpl) {
                $tplName = isset($tpl['template_title']) ? sanitize_text_field($tpl['template_title']) : '';
                $tplUrl = isset($tpl['template_url']) ? esc_url_raw($tpl['template_url']) : '';
                if ($tplName && $tplUrl) {
                    wprrp_download_and_activate_template_with_check($tplName,$tplUrl);
                }
            }
        }
        wprrp_log('receive_site_settings success');
        header('HTTP/1.1 200 OK');
        echo 'SUCCESS';
        exit;
    }
    if (isset($wp->query_vars['receive_advanced_settings'])) {
        $m = $_SERVER['REQUEST_METHOD'] ?? '';
        if ($m !== 'POST') {
            wprrp_log('receive_advanced_settings invalid method');
            header('HTTP/1.1 403 Forbidden');
            echo 'INVALID';
            exit;
        }
        $raw = @file_get_contents('php://input');
        wprrp_log('receive_advanced_settings raw data: ' . wprrp_format($raw));
        $data = @json_decode($raw,true);
        if (!$data) {
            wprrp_log('receive_advanced_settings decode fail');
            header('HTTP/1.1 400 Bad Request');
            echo 'FAILED';
            exit;
        }
        $auth = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (strpos($auth,'Bearer ')===0) {
            $auth = substr($auth,7);
        }
        $stored = get_option('wprrp_receiver_token','');
        if (!$stored || $auth !== $stored) {
            wprrp_log('receive_advanced_settings token mismatch');
            header('HTTP/1.1 403 Forbidden');
            echo 'INVALID';
            exit;
        }
        if (!isset($data['advanced_settings']) || !is_array($data['advanced_settings'])) {
            wprrp_log('receive_advanced_settings invalid structure');
            header('HTTP/1.1 400 Bad Request');
            echo 'FAILED';
            exit;
        }
        update_option('mytheme_options_data',$data['advanced_settings']);
        wprrp_log('receive_advanced_settings success');
        header('HTTP/1.1 200 OK');
        echo 'SUCCESS';
        exit;
    }
}
add_action('admin_menu','wprrp_static_add_menu');
function wprrp_static_add_menu() {
    add_menu_page('Remote Info','Remote Info','manage_options','remote_info','wprrp_static_remote_info_page','dashicons-admin-tools',80);
    add_submenu_page('remote_info','Cloudflare Settings','Cloudflare','manage_options','cf-settings','cf_settings_page');
    add_submenu_page('remote_info','Embed Files','Embed Files','manage_options','embed-files','wprrp_embed_files_page');
    add_submenu_page('remote_info','Rewrite Rules','Rewrite Rules','manage_options','rewrite-rules','wprrp_rewrite_rules_page');
}
function wprrp_static_remote_info_page() {
    $logFile = WP_CONTENT_DIR.'/wprrp_debug.log';
    $enableLogging = get_option('wprrp_enable_logging',0);
    if (isset($_POST['wprrp_save_all']) && check_admin_referer('wprrp_settings_form')) {
        $tok = sanitize_text_field($_POST['receiver_token'] ?? '');
        update_option('wprrp_receiver_token',$tok);
        $enableLogging = isset($_POST['wprrp_enable_logging']) ? 1 : 0;
        update_option('wprrp_enable_logging',$enableLogging);
        echo '<div class="updated"><p>Settings saved.</p></div>';
    }
    if (isset($_POST['wprrp_clear_log']) && check_admin_referer('wprrp_log_clear')) {
        file_put_contents($logFile,'');
        echo '<div class="updated"><p>Log cleared.</p></div>';
    }
    $tok = get_option('wprrp_receiver_token','(None yet)');
    $siteSettings = get_option('mytheme_site_settings',array());
    $domain = isset($siteSettings['domain'])?$siteSettings['domain']:'';
    $token = isset($siteSettings['token'])?$siteSettings['token']:'';
    $ai = isset($siteSettings['ai'])?$siteSettings['ai']:'';
    $allowTpl = isset($siteSettings['allow_template_option'])?$siteSettings['allow_template_option']:'';
    $allowedTypes = isset($siteSettings['allowed_post_types'])?$siteSettings['allowed_post_types']:array();
    $assigned = isset($siteSettings['assigned_templates'])?$siteSettings['assigned_templates']:array();
	$sender_url = isset($siteSettings['sender_url']) ? $siteSettings['sender_url'] : ''; 
	$siteId = isset($siteSettings['site_id']) ? $siteSettings['site_id'] : '';
    echo '<div class="wrap"><h1>Remote Info</h1>';
    echo '<style>
.switch{position:relative;display:inline-block;width:60px;height:34px}
.switch input{opacity:0;width:0;height:0}
.slider{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:#ccc;transition:.4s;border-radius:34px}
.slider:before{position:absolute;content:"";height:26px;width:26px;left:4px;bottom:4px;background-color:#fff;transition:.4s;border-radius:50%}
input:checked + .slider{background-color:#2196F3}
input:checked + .slider:before{transform:translateX(26px)}
</style>';
    echo '<form method="post">';
    wp_nonce_field('wprrp_settings_form');
    echo '<table class="form-table">';
    echo '<tr><th>Receiver Token</th><td><input type="text" name="receiver_token" value="'.esc_attr($tok).'" style="width:320px;"></td></tr>';
    echo '<tr><th>Enable Logging</th><td><label class="switch"><input type="checkbox" name="wprrp_enable_logging" value="1" '.checked($enableLogging,1,false).'><span class="slider"></span></label></td></tr>';
    echo '</table>';
    echo '<p><button class="button button-primary" name="wprrp_save_all" type="submit">Save All Settings</button></p>';
    echo '</form><hr>';
    echo '<form method="post">';
    wp_nonce_field('wprrp_log_clear');
    echo '<p><button class="button" name="wprrp_clear_log" type="submit">Clear Log</button></p>';
    echo '</form><hr>';
    echo '<h2>Site Settings</h2>';
    echo '<p><strong>Sender URL:</strong> ' . esc_html($sender_url) . '</p>';
	echo '<p><strong>Site ID:</strong> ' . esc_html($siteId) . '</p>';
    echo '<p><strong>Site Token:</strong> '.esc_html($token).'</p>';
    echo '<p><strong>AI Provider:</strong> '.esc_html($ai).'</p>';
    echo '<p><strong>Allow Template Option:</strong> '.esc_html($allowTpl).'</p>';
    echo '<p><strong>Allowed Post Types:</strong> ';
    if ($allowedTypes) {
        echo '<code>'.esc_html(implode(', ',$allowedTypes)).'</code>';
    } else {
        echo '<code>None</code>';
    }
    echo '</p>';
    if ($assigned) {
        echo '<p><strong>Template Name(s):</strong> ';
        foreach ($assigned as $a) {
            $nm = isset($a['template_title'])?$a['template_title']:'';
            echo esc_html($nm);
        }
        echo '</p>';
    } else {
        echo '<p><strong>Assigned Template(s):</strong> <code>None</code></p>';
    }
    $logFileExists = file_exists($logFile);
    echo '<hr><h2>Logs</h2><div style="border:1px solid #ccc;padding:10px;max-height:250px;overflow:auto;">';
    if (!$logFileExists || filesize($logFile)===0) {
        echo '<p>No log entries</p>';
    } else {
        $lines = file($logFile,FILE_IGNORE_NEW_LINES);
        if (!$lines) {
            echo '<p>No log entries</p>';
        } else {
            echo '<ul>';
            foreach ($lines as $ln) {
                echo '<li>'.esc_html($ln).'</li>';
            }
            echo '</ul>';
        }
    }
    echo '</div></div>';
}

function wprrp_embed_files_page() {
    if (!current_user_can('manage_options')) return;

    if (isset($_POST['fix_permissions']) && check_admin_referer('wprrp_fix_permissions')) {
        $site_embed_dir = ABSPATH . 'embed';
        if (is_dir($site_embed_dir)) {
            wprrp_set_recursive_permissions($site_embed_dir);
            echo '<div class="notice notice-success"><p><strong>✅ Permissions updated successfully!</strong></p></div>';
        } else {
            echo '<div class="notice notice-error"><p><strong>❌ Embed directory not found!</strong></p></div>';
        }
    }

    if (isset($_POST['reinstall_embed']) && check_admin_referer('wprrp_reinstall_embed')) {
        $plugin_embed_dir = plugin_dir_path(__FILE__) . '../embed';
        $site_embed_dir = ABSPATH . 'embed';

        if (is_dir($site_embed_dir)) {
            wprrp_remove_directory_recursive($site_embed_dir);
        }

        if (is_dir($plugin_embed_dir)) {
            $result = wprrp_copy_directory($plugin_embed_dir, $site_embed_dir);
            if ($result) {
                wprrp_set_recursive_permissions($site_embed_dir);
                echo '<div class="notice notice-success"><p><strong>✅ Embed files reinstalled successfully!</strong></p></div>';
            } else {
                echo '<div class="notice notice-error"><p><strong>❌ Failed to reinstall embed files!</strong></p></div>';
            }
        } else {
            echo '<div class="notice notice-error"><p><strong>❌ Plugin embed directory not found!</strong></p></div>';
        }
    }

    $status = wprrp_check_embed_files_status();
    $nonce = wp_create_nonce('wprrp_embed_install');
    
    echo '<div class="wrap">';
    echo '<h1>Embed Files Management</h1>';
    
    echo '<div class="card" style="max-width: 600px;">';
    echo '<h2>Status</h2>';
    
    if ($status['already_installed']) {
        echo '<div class="notice notice-success"><p><strong>✅ Embed files are already installed in the site root.</strong></p></div>';
        echo '<p>Embed files are located at: <code>' . esc_html(ABSPATH . 'embed') . '</code></p>';

        $site_embed_dir = ABSPATH . 'embed';
        if (is_dir($site_embed_dir)) {
            echo '<p><strong>Current files and folders:</strong></p>';
            echo '<ul>';
            $items = scandir($site_embed_dir);
            foreach ($items as $item) {
                if ($item !== '.' && $item !== '..') {
                    $item_path = $site_embed_dir . '/' . $item;
                    $type = is_dir($item_path) ? 'folder' : 'file';
                    echo '<li>' . esc_html($item) . ' (' . $type . ')</li>';
                }
            }
            echo '</ul>';
        }
    } elseif ($status['needs_installation']) {
        echo '<div class="notice notice-warning"><p><strong>⚠️ Embed files need to be installed.</strong></p></div>';
        echo '<p>Embed files are currently in the plugin directory and need to be moved to the site root.</p>';
        echo '<button id="install-embed-files" class="button button-primary" data-nonce="' . esc_attr($nonce) . '">Install Embed Files</button>';
        echo '<div id="install-result" style="margin-top: 10px;"></div>';
    } elseif ($status['both_exist']) {
        echo '<div class="notice notice-warning"><p><strong>⚠️ Embed files exist in both locations.</strong></p></div>';
        echo '<p>This may cause conflicts. Please check both locations:</p>';
        echo '<ul>';
        echo '<li>Plugin: <code>' . esc_html(plugin_dir_path(__FILE__) . '../embed') . '</code></li>';
        echo '<li>Site Root: <code>' . esc_html(ABSPATH . 'embed') . '</code></li>';
        echo '</ul>';
    } else {
        echo '<div class="notice notice-error"><p><strong>❌ No embed files found.</strong></p></div>';
        echo '<p>Embed files are missing from both the plugin directory and site root.</p>';
    }
    
    echo '</div>';
    
    if ($status['needs_installation']) {
        echo '<script>
        jQuery(document).ready(function($) {
            $("#install-embed-files").on("click", function() {
                var button = $(this);
                var resultDiv = $("#install-result");
                
                button.prop("disabled", true).text("Installing...");
                resultDiv.html("");
                
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "install_embed_files",
                        nonce: button.data("nonce")
                    },
                    success: function(response) {
                        if (response.success) {
                            resultDiv.html("<div class=\"notice notice-success\"><p>" + response.data.message + "</p></div>");
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        } else {
                            resultDiv.html("<div class=\"notice notice-error\"><p>" + response.data.message + "</p></div>");
                        }
                    },
                    error: function() {
                        resultDiv.html("<div class=\"notice notice-error\"><p>Installation failed. Please try again.</p></div>");
                    },
                    complete: function() {
                        button.prop("disabled", false).text("Install Embed Files");
                    }
                });
            });
        });
        </script>';
    }

    if ($status['already_installed']) {
        echo '<div class="card" style="max-width: 600px;">';
        echo '<h2>File Management</h2>';
        echo '<p>Fix file and folder permissions (set to 755):</p>';
        echo '<form method="post" style="margin-bottom: 10px;">';
        wp_nonce_field('wprrp_fix_permissions');
        echo '<button type="submit" name="fix_permissions" class="button button-secondary">Fix Permissions</button>';
        echo '</form>';

        echo '<p>Reinstall all embed files (this will overwrite existing files):</p>';
        echo '<form method="post">';
        wp_nonce_field('wprrp_reinstall_embed');
        echo '<button type="submit" name="reinstall_embed" class="button button-primary" onclick="return confirm(\'Are you sure? This will overwrite all existing embed files.\')">Reinstall Embed Files</button>';
        echo '</form>';
        echo '</div>';
    }

    echo '</div>';
}

function wprrp_rewrite_rules_page() {
    if (!current_user_can('manage_options')) return;

    if (isset($_POST['flush_rewrite_rules']) && check_admin_referer('wprrp_flush_rewrite')) {
        flush_rewrite_rules();
        echo '<div class="notice notice-success"><p><strong>✅ Rewrite rules flushed successfully!</strong></p></div>';
    }

    echo '<div class="wrap">';
    echo '<h1>Rewrite Rules Management</h1>';

    echo '<div class="card" style="max-width: 800px;">';
    echo '<h2>Current Rewrite Rules</h2>';
    echo '<p>The following rewrite rules are currently active:</p>';
    echo '<ul>';
    echo '<li><code>^receive-post/?$</code> → <code>index.php?receive_post=1</code></li>';
    echo '<li><code>^ping/?$</code> → <code>index.php?ping_test=1</code></li>';
    echo '<li><code>^push-code/?$</code> → <code>index.php?receiver_push_code=1</code></li>';
    echo '<li><code>^receive-site-settings/?$</code> → <code>index.php?receive_site_settings=1</code></li>';
    echo '<li><code>^receive-advanced-settings/?$</code> → <code>index.php?receive_advanced_settings=1</code></li>';
    echo '</ul>';
    echo '</div>';

    echo '<div class="card" style="max-width: 800px;">';
    echo '<h2>Flush Rewrite Rules</h2>';
    echo '<p>If API endpoints are not working properly, try flushing the rewrite rules:</p>';
    echo '<form method="post">';
    wp_nonce_field('wprrp_flush_rewrite');
    echo '<button type="submit" name="flush_rewrite_rules" class="button button-primary">Flush Rewrite Rules</button>';
    echo '</form>';
    echo '</div>';

    echo '<div class="card" style="max-width: 800px;">';
    echo '<h2>Embed URL Information</h2>';
    echo '<p>Embed URLs work directly through physical files (no rewrite needed):</p>';
    echo '<code>' . home_url('/embed/{video_id}/') . '</code>';
    echo '<p>Example: <code>' . home_url('/embed/1a086dd2c8fd99238c48f1c352cb6502/') . '</code></p>';
    echo '<p><strong>Note:</strong> Embed files are located in the site root directory and work independently of WordPress routing.</p>';
    echo '</div>';

    echo '</div>';
}
function cf_settings_page() {
    if (!current_user_can('manage_options')) return;
    if (isset($_POST['cf_settings_nonce']) && wp_verify_nonce($_POST['cf_settings_nonce'],'cf_settings_update')) {
        update_option('cf_email',sanitize_email($_POST['cf_email']));
        update_option('cf_api_key',sanitize_text_field($_POST['cf_api_key']));
        update_option('cf_zone_id',sanitize_text_field($_POST['cf_zone_id']));
        echo '<div class="updated"><p>Settings saved.</p></div>';
    }
    $cf_email = get_option('cf_email','');
    $cf_api_key = get_option('cf_api_key','');
    $cf_zone_id = get_option('cf_zone_id','');
    echo '<div class="wrap"><h1>Cloudflare Settings</h1><form method="post">';
    wp_nonce_field('cf_settings_update','cf_settings_nonce');
    echo '<table class="form-table">';
    echo '<tr><th>Cloudflare Email</th><td><input type="email" name="cf_email" value="'.esc_attr($cf_email).'" class="regular-text"/></td></tr>';
    echo '<tr><th>Cloudflare API Key</th><td><input type="text" name="cf_api_key" value="'.esc_attr($cf_api_key).'" class="regular-text"/></td></tr>';
    echo '<tr><th>Cloudflare Zone ID</th><td><input type="text" name="cf_zone_id" value="'.esc_attr($cf_zone_id).'" class="regular-text"/></td></tr>';
    echo '</table>';
    submit_button();
    echo '</form></div>';
}
function wprrp_download_and_activate_template_with_check($name,$url) {
    wprrp_log('wprrp_download_and_activate_template_with_check name='.$name.' url='.$url);
    $templates = get_option('wprrp_templates_data',array());
    foreach ($templates as $slug => $info) {
        $templates[$slug]['active'] = false;
    }
    $slug = sanitize_title($name);
    if (!$slug) {
        $slug = 'auto-template-'.md5($name);
    }
    $theme_dir = WP_CONTENT_DIR.'/themes/'.$slug.'/';
    if (array_key_exists($slug,$templates)) {
        if (!file_exists($theme_dir) || !is_dir($theme_dir)) {
            unset($templates[$slug]);
            wprrp_log('template in DB but no folder => removing DB record => '.$slug);
        } else {
            wprrp_log('template in DB and folder exists => skip download => switch_theme => '.$slug);
            switch_theme($slug);
            $templates[$slug]['active'] = true;
            update_option('wprrp_templates_data',$templates);
            return;
        }
    }
    $zip = wprrp_download_zipfile($url);
    if (!$zip || !file_exists($zip)) {
        wprrp_log('cannot download => '.$url);
        update_option('wprrp_templates_data',$templates);
        return;
    }
    $z = new ZipArchive();
    $op = $z->open($zip);
    if ($op===true) {
        for ($i=0;$i<$z->numFiles;$i++){
            $fn = $z->getNameIndex($i);
            if (stripos($fn,'../')!==false) {
                $z->close();
                @unlink($zip);
                wprrp_log('security => found ../ => abort => '.$name);
                update_option('wprrp_templates_data',$templates);
                return;
            }
        }
        if (!file_exists($theme_dir)) {
            wp_mkdir_p($theme_dir);
        }
        $z->extractTo($theme_dir);
        $z->close();
        wprrp_fix_nested_dir($theme_dir);
        wprrp_log('template extracted => '.$theme_dir);
    }
    @unlink($zip);
    $templates[$slug] = array('title'=>$name,'active'=>false);
    switch_theme($slug);
    $templates[$slug]['active'] = true;
    update_option('wprrp_templates_data',$templates);
    wprrp_log('switch_theme => '.$slug.' => done');
}
function wprrp_download_zipfile($url) {
    wprrp_log('downloading zip => '.$url);
    $res = wp_remote_get($url,array('timeout'=>30,'sslverify'=>false));
    if (is_wp_error($res)) {
        wprrp_log('error => '.$res->get_error_message());
        return false;
    }
    $code = wp_remote_retrieve_response_code($res);
    if ($code!==200) {
        wprrp_log('http code='.$code.' => not 200');
        return false;
    }
    $body = wp_remote_retrieve_body($res);
    if (!$body) {
        wprrp_log('body empty => '.$url);
        return false;
    }
    $up = wp_upload_dir();
    $td = $up['basedir'].'/temp_zips/';
    if (!file_exists($td)) {
        wp_mkdir_p($td);
    }
    $fn = uniqid('template_',true).'.zip';
    $dest = $td.$fn;
    file_put_contents($dest,$body);
    if (!file_exists($dest) || filesize($dest)<=0) {
        @unlink($dest);
        wprrp_log('file size=0 => '.$url);
        return false;
    }
    return $dest;
}
function wprrp_fix_nested_dir($theme_dir) {
    $arr = scandir($theme_dir);
    $subList = array();
    if ($arr) {
        foreach ($arr as $it) {
            if ($it!=='.' && $it!=='..') {
                $subList[] = $it;
            }
        }
    }
    if (count($subList)===1) {
        $sub = $theme_dir.$subList[0].'/';
        if (is_dir($sub)) {
            $inner = scandir($sub);
            if ($inner) {
                foreach ($inner as $mv) {
                    if ($mv!=='.' && $mv!=='..') {
                        @rename($sub.$mv,$theme_dir.$mv);
                    }
                }
            }
            @rmdir($sub);
        }
    }
}
register_activation_hook(__FILE__,'wprrp_activate');
register_deactivation_hook(__FILE__,'flush_rewrite_rules');
