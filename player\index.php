<?php
header("Content-Type: text/html; charset=utf-8");
$video_id = null;
if (isset($_SERVER['REQUEST_URI'])) {
    $request_uri = $_SERVER['REQUEST_URI'];
    if (preg_match('/\/player\/([a-f0-9]{32})\/?/', $request_uri, $matches)) {
        $video_id = $matches[1];
    }
}
if (!$video_id) {
    echo '<div style="color:red">Invalid video ID. URL format should be: /player/{32-character-hex-id}/</div>';
    echo '<div style="color:gray; font-size:12px;">Current URL: ' . htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'unknown') . '</div>';
    exit;
}
$wordpress_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
$secretKey = "m6mljIIxd0MIz8P6W3KK60v1dFyXzllATWtQX6yUCpu5UEDmwRAE2jrQRyfvF";
$now = time();
$exp = $now + 14400;
$r = bin2hex(random_bytes(8));
$raw = $r . ":" . $exp;
$h = hash_hmac("sha256", $raw, $secretKey);
$token = bin2hex($raw . ":" . $h);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="noindex" />
    <title>Video Player</title>
    <script src="https://ssl.p.jwpcdn.com/player/v/8.18.2/jwplayer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/devtools-detector"></script>
    <script src="/player/script/js/init-devtool.js"></script>
    <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/player/sw-loader.php')
                .then(function(registration) {
                    console.log('Service Worker registered with scope:', registration.scope);
                })
                .catch(function(error) {
                    console.error('Service Worker registration failed:', error);
                });
        }
    </script>
    <script>
      function isSafari() {
        const ua = navigator.userAgent;
        const isSafari = /^((?!chrome|android).)*safari/i.test(ua);
        return isSafari;
      }
      function loadScript(src) {
        return new Promise((resolve, reject) => {
          const s = document.createElement("script");
          s.src = src;
          s.onload = resolve;
          s.onerror = reject;
          document.head.appendChild(s);
        });
      }
    </script>
    <style>
      * {margin: 0;padding: 0;}
      body {height: 100vh;}
      .jw-skip {zoom: 1.5;border: 1px solid #ffffff !important;}
      .jw-skip .jw-text,.jw-skip .jw-icon {color: #ffffff !important;}
      .btn-out-ads {position: absolute;bottom: 9em;right: 1em;padding: 0.5em;font-weight: bold;border: 1px solid #ffffff !important;}
      .jw-rightclick {display: none !important;}
      .jw-logo {max-height: 50px;}
      body {height: 100dvh;background-color: #000;}
      .popup {position: fixed;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 350px;max-width: 100%;border-radius: 0.75rem;overflow: hidden;z-index: 9999;background-color: #fff;}
      .popup .body {margin: 1rem 0;display: flex;justify-content: center;align-items: center;flex-direction: column;}
      .popup .body .h1 {font-size: 1.65rem;color: #313131;}
      .popup .body .h2 {font-size: 1.15rem;color: #4d4d4d;}
      .popup .footer {display: flex;justify-content: space-between;align-items: center;border-top: 0.01rem solid rgb(0, 0, 0, 0.2);}
      .popup .footer button:first-child {border-right: 0.01rem solid rgb(0, 0, 0, 0.2);}
      .popup .footer button {width: 100%;border: 0;background: transparent;font-size: 1.05rem;cursor: pointer;padding: 0.35rem 0;}
      .popup .footer button:hover {background: rgb(0, 0, 0, 0.1);}
      .loading-overlay {position: fixed;top: 0;left: 0;width: 100%;height: 100%;background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);display: flex;justify-content: center;align-items: center;z-index: 10000;transition: all 0.5s ease;}
      .loading-container {text-align: center;animation: fadeInUp 0.8s ease-out;}
      .modern-loader {position: relative;width: 120px;height: 120px;margin: 0 auto 30px;}
      .loader-ring {position: absolute;width: 100%;height: 100%;border-radius: 50%;border: 3px solid transparent;}
      .loader-ring:nth-child(1) {border-top: 3px solid #ff6b6b;animation: rotate 2s linear infinite;}
      .loader-ring:nth-child(2) {border-right: 3px solid #4ecdc4;animation: rotate 2s linear infinite reverse;animation-delay: -0.5s;}
      .loader-ring:nth-child(3) {border-bottom: 3px solid #45b7d1;animation: rotate 2s linear infinite;animation-delay: -1s;}
      .loader-ring:nth-child(4) {border-left: 3px solid #f9ca24;animation: rotate 2s linear infinite reverse;animation-delay: -1.5s;}
      .loader-center {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 40px;height: 40px;background: linear-gradient(45deg, #ff6b6b, #4ecdc4);border-radius: 50%;animation: pulse 1.5s ease-in-out infinite;}
      .loading-text {color: #ffffff;font-size: 20px;font-family: 'Arial', sans-serif;font-weight: 600;margin-bottom: 15px;text-shadow: 0 2px 4px rgba(0,0,0,0.3);}
      .loading-subtitle {color: rgba(255,255,255,0.8);font-size: 14px;font-family: 'Arial', sans-serif;animation: breathe 2s ease-in-out infinite;}
      .progress-bar {width: 200px;height: 4px;background: rgba(255,255,255,0.2);border-radius: 2px;margin: 20px auto;overflow: hidden;}
      .progress-fill {height: 100%;background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #f9ca24);border-radius: 2px;animation: progressMove 2s ease-in-out infinite;}
      @keyframes rotate {0% {transform: rotate(0deg);} 100% {transform: rotate(360deg);}}
      @keyframes pulse {0%, 100% {transform: translate(-50%, -50%) scale(1);} 50% {transform: translate(-50%, -50%) scale(1.2);}}
      @keyframes fadeInUp {0% {opacity: 0;transform: translateY(30px);} 100% {opacity: 1;transform: translateY(0);}}
      @keyframes breathe {0%, 100% {opacity: 0.8;} 50% {opacity: 1;}}
      @keyframes progressMove {0% {transform: translateX(-100%);} 100% {transform: translateX(200px);}}
      .loading-overlay.fade-out {opacity: 0;pointer-events: none;transform: scale(0.95);}
    </style>
    <script>
      document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
      });
      document.addEventListener('keydown', function(e) {
        if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I') || (e.ctrlKey && e.shiftKey && e.key === 'C') || (e.ctrlKey && e.key === 'U')) {
          e.preventDefault();
          return false;
        }
      });
      document.addEventListener('selectstart', function(e) {
        e.preventDefault();
        return false;
      });
    </script>
  </head>
  <body>
    <div class="loading-overlay" id="loadingOverlay">
      <div class="loading-container">
        <div class="modern-loader">
          <div class="loader-ring"></div>
          <div class="loader-ring"></div>
          <div class="loader-ring"></div>
          <div class="loader-ring"></div>
          <div class="loader-center"></div>
        </div>
        <div class="loading-text">กำลังโหลดวีดีโอ</div>
        <div class="loading-subtitle">กรุณารอสักครู่...</div>
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
      </div>
    </div>
    <div id="player"></div>
    <script>
      var v="<?php echo htmlspecialchars($video_id,ENT_QUOTES,'UTF-8');?>";
      var token="<?php echo htmlspecialchars($token,ENT_QUOTES,'UTF-8');?>";
      var wordpressUrl="<?php echo htmlspecialchars($wordpress_url,ENT_QUOTES,'UTF-8');?>";

      if (typeof devtools !== 'undefined') {
        devtools.open = function() {
          window.location.href = 'about:blank';
        };
        devtools.orientation.changed = function() {
          window.location.href = 'about:blank';
        };
      }

      (async () => {
        if (!isSafari()) {
          try {
            await loadScript("https://fastly.jsdelivr.net/gh/zergolf1994/p2p-jwplayer@main/p2p-media-loader-core.min.js");
            await loadScript("https://fastly.jsdelivr.net/gh/zergolf1994/p2p-jwplayer@main/p2p-media-loader-hlsjs.min.js");
            await loadScript("https://fastly.jsdelivr.net/gh/zergolf1994/p2p-jwplayer@main/hls.min.js");
            await loadScript("https://fastly.jsdelivr.net/gh/zergolf1994/p2p-jwplayer@main/jwplayer.hlsjs.min.js");
            initPlayer();
          } catch (e) {
            console.error("Script loading failed:", e);
          }
        } else {
          initPlayer();
        }
      })();
      function initPlayer() {
        showLoading();
        getVideoUrl();
      }
      function showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
          overlay.style.display = 'flex';
          overlay.classList.remove('fade-out');
        }
      }
      function hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
          overlay.classList.add('fade-out');
          setTimeout(() => {
            overlay.style.display = 'none';
          }, 300);
        }
      }
      function getVideoUrl() {
        if (typeof wordpressUrl === 'undefined' || !wordpressUrl) {
          showError('WordPress URL not configured');
          return;
        }
        console.log('Video ID:', v);
        console.log('Token:', token.substring(0, 20) + '...');
        console.log('WordPress URL:', wordpressUrl);
        var xhr = new XMLHttpRequest();
        xhr.open('POST', wordpressUrl + '/wp-admin/admin-ajax.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            console.log('Response status:', xhr.status);
            console.log('Response text:', xhr.responseText);
            if (xhr.status === 200) {
              try {
                var response = JSON.parse(xhr.responseText);
                if (response.success && response.data && response.data.url) {
                  setupJWPlayer(response.data.url);
                } else {
                  showError('Video not found: ' + (response.data ? response.data.message : 'Unknown error'));
                }
              } catch (e) {
                showError('Invalid response from server: ' + e.message);
              }
            } else {
              showError('Server error: ' + xhr.status + ' - ' + xhr.responseText);
            }
          }
        };
        var formData = 'action=get_video_url&video_id=' + encodeURIComponent(v) + '&nonce=' + encodeURIComponent(token);
        xhr.send(formData);
      }
      function setupJWPlayer(videoUrl) {
        const player = jwplayer("player");
        const objSetup = {
          key: "W7zSm81+mmIsg7F+fyHRKhF3ggLkTqtGMhvI92kbqf/ysE99",
          width: "100%",
          height: "100%",
          preload: "metadata",
          primary: "html5",
          hlshtml: "true",
          controls: "true",
          pipIcon: "true",
          autostart: false,
          mute: false,
          repeat: false,
          sources: [
            { file: videoUrl, type: "application/vnd.apple.mpegurl" },
          ],
          skin: {
            controlbar: { iconsActive: "#ffffff" },
            timeslider: { progress: "#ffffff" },
            menus: { background: "#121212", textActive: "#ffffff" },
          },
          advertising: {},
        };
        const segments_in_queue = 50;
        let engine_config = { 
          loader: {
            httpFailedSegmentTimeout: 10000,
            httpDownloadMaxPriority: 5,    
            cachedSegmentsCount: 200,    
            requiredSegmentsPriority: 10,            
            simultaneousP2PDownloads: 8,          
            simultaneousHttpDownloads: 2,   
            httpDownloadProbabilitySkipIfNoPeers: true ,
            httpDownloadProbabilityInterval: 3000, 
            httpDownloadInitialTimeout: 2000,   
            segmentDownloadTimeout: 3000,       
            trackerAnnounce: ["wss://tracker.btorrent.xyz"],
            rtcConfig: {
              iceServers: [
                { urls: "stun:stun.l.google.com:19302" },
                { urls: "stun:global.stun.twilio.com:3478" }
              ]
            },
          },
        };
        if (!isSafari()) {
          if (typeof Hls !== 'undefined' && Hls.isSupported() && typeof p2pml !== 'undefined' && p2pml.hlsjs.Engine.isSupported()) {
            const engine = new p2pml.hlsjs.Engine(engine_config);
            player.setup(objSetup);
            jwplayer_hls_provider.attach();
            p2pml.hlsjs.initJwPlayer(player, { 
              maxBufferLength: 300,
              loader: engine.createLoaderClass(),
            });
          } else {
            player.setup(objSetup);
          }
        } else {
          player.setup(objSetup);
        }
        player.on("ready", function (evt) {
          console.log('Player ready');
          hideLoading();
        });
        player.on("error", function(e) {
          console.error('JW Player Error:', e);
          showError('Player Error: ' + e.message + ' (Code: ' + e.code + ')');
        });
      }
      function showError(message) {
        console.error(message);
        hideLoading();
        document.body.innerHTML = '<div style="color: red; text-align: center; padding: 50px; font-size: 18px;">' + message + '</div>';
      }
    </script>
  </body>
</html>
