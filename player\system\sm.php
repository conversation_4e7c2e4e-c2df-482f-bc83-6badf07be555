<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: *");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    exit;
}

$file = isset($_GET["file"]) ? trim($_GET["file"]) : "";
$method = isset($_GET["method"]) ? trim($_GET["method"]) : "redirect";
$origin_domain_param = isset($_GET["origin_domain"]) ? trim($_GET["origin_domain"]) : "";

if (!$file) {
    http_response_code(400);
    echo "Missing file parameter";
    exit;
}

$domain_to_use = !empty($origin_domain_param) ? $origin_domain_param : parseDomain($file);
if (!$domain_to_use) {
    http_response_code(400);
    echo "Could not determine origin domain";
    exit;
}

switch ($method) {
    case "redirect":
        handleRedirect($file, $domain_to_use);
        break;
    case "headers":
        handleHeadersOnly($file, $domain_to_use);
        break;
    case "stream":
        handleStreamRedirect($file, $domain_to_use);
        break;
    case "js":
        handleJavaScriptRedirect($file, $domain_to_use);
        break;
    default:
        handleSmartProxy($file, $domain_to_use);
        break;
}

function handleRedirect($file, $domain) {
    $originHeader = "https://" . $domain;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $file);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
    curl_setopt($ch, CURLOPT_REFERER, $originHeader . "/");
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Origin: " . $originHeader
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $ct = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);

    if ($code >= 200 && $code < 300) {
        if ($ct) header("Content-Type: " . $ct);
        header("Location: " . $file);
        header("Cache-Control: public, max-age=300");
        http_response_code(302);
    } else {
        http_response_code($code ?: 404);
        echo "File not accessible: HTTP " . $code;
    }
    exit;
}

function handleHeadersOnly($file, $domain) {
    $originHeader = "https://" . $domain;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $file);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
    curl_setopt($ch, CURLOPT_REFERER, $originHeader . "/");
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Origin: " . $originHeader,
        "Accept: application/vnd.apple.mpegurl,*/*"
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $response = curl_exec($ch);
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $ct = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($code >= 200 && $code < 300 && $response) {
        if ($ct) header("Content-Type: " . $ct);
        header("Content-Length: " . strlen($response));
        echo $response;
    } else {
        http_response_code($code ?: 500);
        echo "Error: HTTP " . $code . ($error ? " - " . $error : "");
    }
}

function handleStreamRedirect($file, $domain) {
    $originHeader = "https://" . $domain;
    $range = isset($_SERVER['HTTP_RANGE']) ? $_SERVER['HTTP_RANGE'] : '';
    if ($range) {
        header("Location: " . $file);
        header("Accept-Ranges: bytes");
        http_response_code(206);
    } else {
        header("Location: " . $file);
        http_response_code(302);
    }
    exit;
}

function handleJavaScriptRedirect($file, $domain) {
    header("Content-Type: text/html");
    echo generateRedirectHTML($file, $domain);
}

function handleSmartProxy($file, $domain) {
    $originHeader = "https://" . $domain;

    if (strpos($file, '.m3u8') !== false) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $file);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15");
        curl_setopt($ch, CURLOPT_REFERER, $originHeader . "/");
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Origin: " . $originHeader,
            "Accept: application/vnd.apple.mpegurl,*/*",
            "Accept-Language: en-US,en;q=0.9",
            "Cache-Control: no-cache",
            "Pragma: no-cache"
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($code >= 200 && $code < 300 && $response && strlen($response) > 0) {
            $processedContent = processM3U8ForZeroBandwidth($response, $file);

            header("Content-Type: application/vnd.apple.mpegurl");
            header("Content-Length: " . strlen($processedContent));
            header("Cache-Control: public, max-age=300");
            echo $processedContent;
        } else {
            http_response_code($code ?: 500);
            echo "Error fetching M3U8: HTTP " . $code . ($error ? " - " . $error : "");
        }
    } else {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $file);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15");
        curl_setopt($ch, CURLOPT_REFERER, $originHeader . "/");
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Origin: " . $originHeader,
            "Accept: video/mp2t,*/*"
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $ct = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        curl_close($ch);

        if ($code >= 200 && $code < 300) {
            if ($ct) header("Content-Type: " . $ct);
            header("Location: " . $file);
            header("Cache-Control: public, max-age=3600");
            http_response_code(302);
        } else {
            http_response_code($code ?: 404);
            echo "Segment not accessible: HTTP " . $code;
        }
    }
}

function processM3U8ForZeroBandwidth($content, $baseUrl) {
    $lines = explode("\n", $content);
    $processedLines = [];
    $baseParsedUrl = parse_url($baseUrl);
    $baseScheme = isset($baseParsedUrl['scheme']) ? $baseParsedUrl['scheme'] : 'http';
    $baseHost = isset($baseParsedUrl['host']) ? $baseParsedUrl['host'] : '';
    $basePath = isset($baseParsedUrl['path']) ? dirname($baseParsedUrl['path']) : '';
    $baseOrigin = $baseScheme . '://' . $baseHost;

    foreach ($lines as $line) {
        $line = trim($line);

        if (empty($line) || strpos($line, '#') === 0) {
            $processedLines[] = $line;
            continue;
        }
        if (preg_match('/^https?:\/\//', $line)) {
            $processedLines[] = $line;
        } 
        elseif (strpos($line, '/') === 0) {
            $processedLines[] = $baseOrigin . $line;
        }
        else {
            $absoluteUrl = $baseOrigin . rtrim($basePath, '/') . '/' . ltrim($line, '/');
            $processedLines[] = $absoluteUrl;
        }
    }
    
    return implode("\n", $processedLines);
}

function makeZeroBandwidthUrl($url) {
    $currentScript = $_SERVER['PHP_SELF'];
    return $currentScript . '?method=stream&file=' . urlencode($url);
}

function generateFetchScript($file, $domain) {
    return '
<script>
(async function() {
    try {
        const response = await fetch("' . $file . '", {
            headers: {
                "Origin": "https://' . $domain . '",
                "Referer": "https://' . $domain . '/"
            }
        });
        
        if (response.ok) {
            const content = await response.text();
            document.body.innerHTML = "<pre>" + content + "</pre>";
        } else {
            document.body.innerHTML = "Error: " + response.status;
        }
    } catch (error) {
        document.body.innerHTML = "Error: " + error.message;
    }
})();
</script>';
}

function generateRedirectHTML($file, $domain) {
    return '<!DOCTYPE html>
<html>
<head>
    <title>Zero Bandwidth Redirect</title>
    <script>
        window.location.replace("' . $file . '");
        setTimeout(async function() {
            try {
                const response = await fetch("' . $file . '", {
                    headers: {
                        "Origin": "https://' . $domain . '",
                        "Referer": "https://' . $domain . '/"
                    }
                });
                
                if (response.ok) {
                    const content = await response.text();
                    document.body.innerHTML = "<pre>" + content + "</pre>";
                }
            } catch (error) {
                document.body.innerHTML = "Error: " + error.message;
            }
        }, 1000);
    </script>
</head>
<body>
    <p>Redirecting to zero bandwidth source...</p>
</body>
</html>';
}

function parseDomain($url) {
    if (!preg_match('/^(https?:\/\/([^\/]+))(\/.*)?$/', $url, $m)) {
        return "";
    }
    return $m[2];
}
?>
