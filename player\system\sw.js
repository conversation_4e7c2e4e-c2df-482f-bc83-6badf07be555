const TARGET_PATHS = [
    '.m3u8',
    '.ts',
    '.aaa',
    '.m4s',
    '.txt',
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '/m3u8/',
    '/hls/',
    '/txt/'
];

let sessionHeaders = new Map();
let domainsRequiringProxy = new Set();
let domainToProxyMap = new Map();

function getUrlKey(url) {
    return url.hostname;
}

function shouldInterceptRequest(url) {
    const pathname = url.pathname.toLowerCase();
    const matchingPath = TARGET_PATHS.find(path => pathname.includes(path));
    return !!matchingPath;
}

function storeSessionHeaders(response, url, urlKey) {
    const setCookie = response.headers.get('set-cookie');
    if (setCookie) {
        sessionHeaders.set(urlKey, setCookie);
        console.log('[SW] Stored session headers for:', urlKey);
    }
}

function getContentType(url) {
    if (url.pathname.includes('.m3u8')) {
        return {
            type: 'playlist',
            isPlaylist: true,
            contentType: 'application/vnd.apple.mpegurl',
            accept: 'application/vnd.apple.mpegurl,*/*',
        };
    } else if (url.pathname.includes('.ts') || url.pathname.includes('.aaa')) {
        return {
            type: 'segment',
            isPlaylist: false,
            contentType: 'video/mp2t',
            accept: 'video/mp2t,*/*',
        };
    }
    return {
        type: 'unknown',
        isPlaylist: false,
        contentType: 'application/octet-stream',
        accept: '*/*',
    };
}

function createOptimalHeaders(url, urlKey, contentType) {
    const headers = new Headers();
    const origin = url.origin; 

    headers.set('Accept', contentType.accept);
    headers.set('Origin', origin);
    headers.set('Referer', origin + '/');
    
    headers.set('User-Agent', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1');
    const storedCookie = sessionHeaders.get(urlKey);
    if (storedCookie) {
        headers.set('Cookie', storedCookie);
    }
    return headers;
}

function createCORSHeaders(contentType) {
    const headers = new Headers();
    headers.set('Access-Control-Allow-Origin', '*');
    headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Range');
    headers.set('Access-Control-Expose-Headers', 'Content-Length, Content-Range');
    headers.set('Content-Type', contentType);
    return headers;
}

function processM3U8Content(content, baseUrl) {
    const lines = content.split('\n');
    const processedLines = [];
    
    for (let line of lines) {
        line = line.trim();
        
        if (line && !line.startsWith('#')) {
            if (!line.startsWith('http')) {
                try {
                    const absoluteUrl = new URL(line, baseUrl).href;
                    processedLines.push(absoluteUrl);
                } catch (e) {
                    processedLines.push(line);
                }
            } else {
                processedLines.push(line);
            }
        } else {
            processedLines.push(line);
        }
    }
    
    return processedLines.join('\n');
}

let proxyList = []; 

async function updateProxyList() {
    try {
        const response = await fetch('system/proxies.json');
        if (response.ok) {
            const list = await response.json();
            if (Array.isArray(list) && list.length > 0) {
                proxyList = list;
                console.log('[SW] Proxy list updated successfully from proxies.json:', proxyList);
            } else {
                console.error('[SW] proxies.json is empty or not a valid array. Proxy functionality will be disabled.');
                proxyList = [];
            }
        } else {
             console.error(`[SW] Failed to fetch proxies.json, status: ${response.status}. Proxy functionality will be disabled.`);
             proxyList = [];
        }
    } catch (error) {
        console.error('[SW] Error fetching or parsing proxies.json. Proxy functionality will be disabled.', error);
        proxyList = [];
    }
}

function getRandomProxy() {
    if (proxyList.length === 0) {
        return null; 
    }
    const randomIndex = Math.floor(Math.random() * proxyList.length);
    return proxyList[randomIndex];
}

function getProxyForDomain(hostname) {
    if (domainToProxyMap.has(hostname)) {
        return domainToProxyMap.get(hostname);
    }
    const newProxy = getRandomProxy();
    if (newProxy) {
        console.log(`[SW] Assigning new proxy ${newProxy} to domain ${hostname}`);
        domainToProxyMap.set(hostname, newProxy);
    }
    return newProxy;
}

self.addEventListener('install', (event) => {
    console.log('[SW] Installing...');
    event.waitUntil(updateProxyList().then(() => self.skipWaiting()));
});

self.addEventListener('activate', (event) => {
    console.log('[SW] Activating...');
    event.waitUntil(self.clients.claim());
});

self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);

    if (shouldInterceptRequest(url)) {
        event.respondWith((async () => {
            const contentType = getContentType(url);
            const hostname = url.hostname;

            if (contentType.isPlaylist) {
                try {
                    const response = await fetch(request.url, { headers: createOptimalHeaders(url, getUrlKey(url), contentType) });
                    const content = await response.text();
                    if (!response.ok || !content) throw new Error('Empty or failed playlist response');
                    storeSessionHeaders(response, url, getUrlKey(url));
                    return new Response(processM3U8Content(content, request.url), { status: 200, headers: createCORSHeaders(contentType.contentType) });
                } catch (error) {
                    const proxyUrl = getProxyForDomain(hostname);
                    if (!proxyUrl) return new Response('Playlist fetch failed: No proxy available', { status: 503 });
                    return fetchWithProxy(request, url, contentType, proxyUrl, true);
                }
            } else { // It's a segment
                let useProxy = domainsRequiringProxy.has(hostname);
                if (useProxy) {
                    const proxyUrl = getProxyForDomain(hostname);
                    if (!proxyUrl) return new Response('Segment fetch failed: No proxy available', { status: 503 });
                    return fetchWithProxy(request, url, contentType, proxyUrl, false);
                } else {
                    try {
                        const directResponse = await fetch(request.url, { headers: createOptimalHeaders(url, getUrlKey(url), contentType) });
                        if (!directResponse.ok) throw new Error('Segment direct fetch failed');
                        return directResponse;
                    } catch (error) {
                        domainsRequiringProxy.add(hostname);
                        const proxyUrl = getProxyForDomain(hostname);
                        if (!proxyUrl) return new Response('Segment fetch failed: No proxy available', { status: 503 });
                        return fetchWithProxy(request, url, contentType, proxyUrl, false);
                    }
                }
            }
        })());
    }
});

async function fetchWithProxy(request, url, contentType, proxyUrl, isPlaylist) {
    const method = isPlaylist ? 'smart' : 'headers';
    try {
        const originDomain = url.hostname;
        const finalProxyUrl = `${proxyUrl}?method=${method}&file=${encodeURIComponent(url.href)}&origin_domain=${encodeURIComponent(originDomain)}`;
        const proxyResponse = await fetch(finalProxyUrl, { headers: request.headers });
        if (!proxyResponse.ok) throw new Error(`Proxy failed with status ${proxyResponse.status}`);
        
        const responseHeaders = createCORSHeaders(contentType.contentType);
        return new Response(proxyResponse.body, { status: 200, headers: responseHeaders });
    } catch (proxyError) {
        return new Response(`Proxy fetch failed: ${proxyError.message}`, { status: 502 });
    }
} 