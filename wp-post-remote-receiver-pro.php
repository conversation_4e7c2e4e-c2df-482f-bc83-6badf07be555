<?php
/*
Plugin Name: WP Post Remote Receiver PRO
Plugin URI: https://example.com
Description: v1.1 - Receiver with detailed logging and template handling.
Version: 1.1
Author: Your Name
License: GPLv2
Text Domain: wp-post-remote-receiver-pro
*/
if(!defined('ABSPATH')) exit;
require_once plugin_dir_path(__FILE__).'includes/receiver-static.php';
require_once plugin_dir_path(__FILE__).'includes/receiver-dynamic.php';

register_activation_hook(__FILE__,'wprrp_activate_main');
register_deactivation_hook(__FILE__,'wprrp_deactivate_main');
register_uninstall_hook(__FILE__, 'wprrp_uninstall_main');

function wprrp_activate_main(){
    wprrp_install_embed_files();

    $site_embed_dir = ABSPATH . 'embed';
    $site_player_dir = ABSPATH . 'player';
    if (is_dir($site_embed_dir)) {
        wprrp_set_recursive_permissions($site_embed_dir);
    }
    if (is_dir($site_player_dir)) {
        wprrp_set_recursive_permissions($site_player_dir);
    }

    flush_rewrite_rules();
}

function wprrp_flush_rewrite_rules() {
    flush_rewrite_rules();
}
add_action('wp_ajax_flush_rewrite_rules', 'wprrp_flush_rewrite_rules');

function wprrp_deactivate_main(){
    flush_rewrite_rules();
}

function wprrp_uninstall_main(){
    $site_embed_dir = ABSPATH . 'embed';
    $site_player_dir = ABSPATH . 'player';
    if (is_dir($site_embed_dir)) {
        wprrp_remove_directory_recursive($site_embed_dir);
    }
    if (is_dir($site_player_dir)) {
        wprrp_remove_directory_recursive($site_player_dir);
    }
    delete_option('wprrp_enable_logging');
    delete_option('wprrp_receiver_token');
    flush_rewrite_rules();
}

function wprrp_install_embed_files() {
    $plugin_embed_dir = plugin_dir_path(__FILE__) . 'embed';
    $plugin_player_dir = plugin_dir_path(__FILE__) . 'player';
    $site_root = ABSPATH;
    $site_embed_dir = $site_root . 'embed';
    $site_player_dir = $site_root . 'player';

    if (!is_dir($plugin_embed_dir) || !is_dir($plugin_player_dir)) {
        return false;
    }

    if (is_dir($site_embed_dir) && is_dir($site_player_dir)) {
        return true;
    }

    if (!wp_mkdir_p($site_embed_dir) || !wp_mkdir_p($site_player_dir)) {
        return false;
    }

    $embed_result = wprrp_copy_directory($plugin_embed_dir, $site_embed_dir);
    $player_result = wprrp_copy_directory($plugin_player_dir, $site_player_dir);

    if ($embed_result && $player_result) {
        wprrp_set_recursive_permissions($site_embed_dir);
        wprrp_set_recursive_permissions($site_player_dir);
        wprrp_remove_plugin_embed_dir($plugin_embed_dir);
        wprrp_remove_plugin_embed_dir($plugin_player_dir);
        return true;
    }

    return false;
}

function wprrp_copy_directory($source, $destination) {
    if (!is_dir($source)) {
        return false;
    }

    if (!is_dir($destination)) {
        if (!wp_mkdir_p($destination)) {
            return false;
        }
        wprrp_set_permissions($destination, 0755);
    }

    $dir = opendir($source);
    if (!$dir) {
        return false;
    }

    $success = true;

    while (($file = readdir($dir)) !== false) {
        if ($file === '.' || $file === '..') {
            continue;
        }

        $source_path = $source . '/' . $file;
        $dest_path = $destination . '/' . $file;

        if (is_dir($source_path)) {
            if (!wprrp_copy_directory($source_path, $dest_path)) {
                $success = false;
            }
        } else {
            if (!copy($source_path, $dest_path)) {
                $success = false;
            } else {
                wprrp_set_permissions($dest_path, 0755);
            }
        }
    }

    closedir($dir);
    return $success;
}

function wprrp_set_permissions($path, $permissions) {
    if (!file_exists($path)) {
        return false;
    }

    if (PHP_OS_FAMILY === 'Windows') {
        return true;
    }

    return @chmod($path, $permissions);
}

function wprrp_set_recursive_permissions($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    wprrp_set_permissions($dir, 0755);

    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );

    foreach ($iterator as $item) {
        if ($item->isDir()) {
            wprrp_set_permissions($item->getPathname(), 0755);
        } else {
            wprrp_set_permissions($item->getPathname(), 0755);
        }
    }

    return true;
}

function wprrp_remove_plugin_embed_dir($dir) {
    if (!is_dir($dir)) {
        return true;
    }

    $files = array_diff(scandir($dir), array('.', '..'));

    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            wprrp_remove_plugin_embed_dir($path);
        } else {
            unlink($path);
        }
    }

    return rmdir($dir);
}

function wprrp_remove_directory_recursive($dir) {
    if (!is_dir($dir)) {
        return true;
    }

    $files = array_diff(scandir($dir), array('.', '..'));

    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            wprrp_remove_directory_recursive($path);
        } else {
            unlink($path);
        }
    }

    return rmdir($dir);
}

function wprrp_check_embed_files_status() {
    $site_embed_dir = ABSPATH . 'embed';
    $plugin_embed_dir = plugin_dir_path(__FILE__) . 'embed';
    
    $site_has_embed = is_dir($site_embed_dir);
    $plugin_has_embed = is_dir($plugin_embed_dir);
    
    return array(
        'site_has_embed' => $site_has_embed,
        'plugin_has_embed' => $plugin_has_embed,
        'needs_installation' => $plugin_has_embed && !$site_has_embed,
        'already_installed' => $site_has_embed && !$plugin_has_embed,
        'both_exist' => $site_has_embed && $plugin_has_embed
    );
}

add_action('wp_ajax_install_embed_files', 'wprrp_ajax_install_embed_files');
function wprrp_ajax_install_embed_files() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    check_ajax_referer('wprrp_embed_install', 'nonce');
    
    $result = wprrp_install_embed_files();
    
    if ($result) {
        wp_send_json_success(array('message' => 'Embed files installed successfully'));
    } else {
        wp_send_json_error(array('message' => 'Failed to install embed files'));
    }
}